<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>org.springframework.boot</groupId>
		<artifactId>spring-boot-starter-parent</artifactId>
		<version>3.5.5</version> <!-- lookup parent from repository -->
	</parent>
	<groupId>vinsf</groupId>
	<artifactId>miniapp-service</artifactId>
	<version>1.0-SNAPSHOT</version>
	<name>miniapp-service</name>
	<description>MiniApp Service</description>

	<properties>
		<java.version>21</java.version>
		<aws.java.sdk.version>2.31.57</aws.java.sdk.version>
		<postgresql.version>42.7.6</postgresql.version>
	</properties>

	<dependencies>
		
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-data-jpa</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-web</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-security</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-validation</artifactId>
		</dependency>

		<dependency>
			<groupId>org.springframework.kafka</groupId>
			<artifactId>spring-kafka</artifactId>
		</dependency>
		<dependency>
			<groupId>org.redisson</groupId>
			<artifactId>redisson</artifactId>
			<version>3.49.0</version>
		</dependency>
		<dependency>
			<groupId>com.github.ben-manes.caffeine</groupId>
			<artifactId>caffeine</artifactId>
			<version>3.1.8</version>
		</dependency>
		<dependency>
			<groupId>org.apache.httpcomponents.client5</groupId>
			<artifactId>httpclient5</artifactId>
			<version>5.5</version>
            <exclusions>
                <exclusion>
                    <groupId>commons-logging</groupId>
                    <artifactId>commons-logging</artifactId>
                </exclusion>
            </exclusions>
		</dependency>

		<dependency>
			<groupId>com.fasterxml.uuid</groupId>
			<artifactId>java-uuid-generator</artifactId>
			<version>5.1.0</version>
		</dependency>

		<dependency>
			<groupId>org.mapstruct</groupId>
			<artifactId>mapstruct</artifactId>
			<version>1.5.5.Final</version>
		</dependency>
		<dependency>
			<groupId>org.mapstruct</groupId>
			<artifactId>mapstruct-processor</artifactId>
			<version>1.5.5.Final</version>
			<scope>provided</scope>
		</dependency>
        <dependency>
            <groupId>io.hypersistence</groupId>
            <artifactId>hypersistence-utils-hibernate-63</artifactId>
            <version>3.11.0</version>
        </dependency>

		<!-- AWS SDK Dependencies -->
        <dependency>
            <groupId>software.amazon.awssdk</groupId>
            <artifactId>kms</artifactId>
            <version>${aws.java.sdk.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>commons-logging</groupId>
                    <artifactId>commons-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>software.amazon.awssdk</groupId>
            <artifactId>s3</artifactId>
            <version>${aws.java.sdk.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>commons-logging</groupId>
                    <artifactId>commons-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
<!--        <dependency>-->
<!--            <groupId>software.amazon.awssdk</groupId>-->
<!--            <artifactId>cloudfront</artifactId>-->
<!--            <version>${aws.java.sdk.version}</version>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>software.amazon.awssdk</groupId>-->
<!--            <artifactId>sts</artifactId>-->
<!--            <version>${aws.java.sdk.version}</version>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>software.amazon.awssdk</groupId>-->
<!--            <artifactId>sso</artifactId>-->
<!--            <version>${aws.java.sdk.version}</version>-->
<!--        </dependency>-->

		<dependency>
			<groupId>org.postgresql</groupId>
			<artifactId>postgresql</artifactId>
			<version>${postgresql.version}</version>
		</dependency>

		<dependency>
			<groupId>org.projectlombok</groupId>
			<artifactId>lombok</artifactId>
			<version>1.18.38</version>
		</dependency>

<!--		<dependency>-->
<!--			<groupId>tech.vinsf</groupId>-->
<!--			<artifactId>vinsf-common-java21sprb3</artifactId>-->
<!--			<version>1.0.0</version>-->
<!--		</dependency>-->

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-test</artifactId>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.springframework.kafka</groupId>
			<artifactId>spring-kafka-test</artifactId>
			<scope>test</scope>
		</dependency>
	</dependencies>

	<build>
		<plugins>
			<plugin>
				<groupId>io.fabric8</groupId>
				<artifactId>docker-maven-plugin</artifactId>
				<version>0.46.0</version>
				<configuration>
					<skip>${skipDockerMaven}</skip>
					<images>
						<image>
							<alias>postgresql17_test</alias>
							<name>public.ecr.aws/docker/library/postgres:17.5</name>
							<run>
								<env>
									<POSTGRES_USER>vinsf_user</POSTGRES_USER>
									<POSTGRES_PASSWORD>vinsf_pass</POSTGRES_PASSWORD>
									<POSTGRES_DB>vinsf_db</POSTGRES_DB>
									<POSTGRES_SCHEMA>miniapp_db</POSTGRES_SCHEMA>
								</env>
								<ports>
									<port>2432:5432</port>
								</ports>
								<volumes>
									<bind>
										<volume>src/test/data/pg_docker_init.sh:/docker-entrypoint-initdb.d/db.sh</volume>
									</bind>
								</volumes>
								<log>
									<enabled>true</enabled>
								</log>
								<wait>
									<log>database system is ready to accept connections</log>
									<time>20000</time>
								</wait>
							</run>
						</image>

                        <image>
                            <name>docker.io/library/redis:7.4</name>
                            <alias>redis_test</alias>
                            <run>
                                <ports>
                                    <port>6379:6379</port>
                                </ports>
                                <cmd>
                                    <exec>
                                        <arg>redis-server</arg>
                                        <arg>--appendonly</arg><arg>no</arg>
                                        <arg>--maxmemory</arg><arg>200mb</arg>
                                        <arg>--maxmemory-policy</arg><arg>allkeys-lru</arg>
                                    </exec>
                                </cmd>
                                <log>
                                    <enabled>false</enabled>
                                </log>
                            </run>
                        </image>


                        <image>
							<name>docker.io/library/zookeeper:3.9.4</name>
							<alias>zookeeper</alias>
							<run>
								<ports>
									<port>2181:2181</port>
								</ports>
								<wait>
									<log>binding to port</log>
									<time>60000</time>
								</wait>
								<env>
									<ALLOW_ANONYMOUS_LOGIN>yes</ALLOW_ANONYMOUS_LOGIN>
								</env>
							</run>
						</image>

						<image>
							<name>bitnami/kafka:3.5.1</name>
							<alias>kafka_test</alias>
							<run>
								<ports>
									<port>9092:9092</port>
									<port>9093:9093</port>
									<port>9094:9094</port>
								</ports>
								<links>
									<link>zookeeper:zk</link>
								</links>
								<env>
									<KAFKA_CLIENT_USERS>vinsf-user</KAFKA_CLIENT_USERS>
									<KAFKA_CLIENT_PASSWORDS>vinsf-pass</KAFKA_CLIENT_PASSWORDS>
									<KAFKA_CFG_ADVERTISED_LISTENERS>PLAINTEXT://localhost:9092</KAFKA_CFG_ADVERTISED_LISTENERS>
									<KAFKA_ZOOKEEPER_PROTOCOL>PLAINTEXT</KAFKA_ZOOKEEPER_PROTOCOL>
									<KAFKA_CFG_ZOOKEEPER_CONNECT>zk:2181</KAFKA_CFG_ZOOKEEPER_CONNECT>
								</env>
								<wait>
									<log>started \(kafka\.server\.KafkaServer\)</log>
									<time>60000</time>
								</wait>
								<log>
									<enabled>false</enabled>
								</log>
							</run>
						</image>

                        <!-- LocalStack (KMS/STS/S3) -->
                        <image>
                            <alias>localstack_aws</alias>
                            <name>localstack/localstack:3.7</name>
                            <run>
                                <env>
                                    <SERVICES>kms,sts,s3</SERVICES>
                                    <DEBUG>0</DEBUG>
                                    <AWS_DEFAULT_REGION>ap-southeast-1</AWS_DEFAULT_REGION>
                                    <BUCKET_NAME>vfs-miniapp-bucket</BUCKET_NAME>
                                    <KMS_ALIAS>alias/vfs-miniapp-encryption-key</KMS_ALIAS>
                                </env>
                                <ports>
                                    <port>4566:4566</port>
                                </ports>
                                <volumes>
                                    <bind>
                                        <volume>tmp/localstack_data:/var/lib/localstack</volume>
                                    </bind>
                                    <bind>
                                        <volume>/var/run/docker.sock:/var/run/docker.sock</volume>
                                    </bind>
                                </volumes>
                                <log>
                                    <enabled>true</enabled>
                                </log>
                                <wait>
                                    <log>Ready.</log>
                                    <time>60000</time>
                                </wait>
                            </run>
                        </image>

                    </images>
				</configuration>
				<executions>
					<execution>
						<id>start_mdp</id>
						<!--<phase>generate-test-resources</phase>-->
						<phase>process-test-classes</phase>
						<!--<phase>test</phase>-->
						<goals>
							<goal>start</goal>
						</goals>
					</execution>
					<execution>
						<id>stop</id>
						<phase>test</phase>
						<goals>
							<goal>stop</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<groupId>org.codehaus.mojo</groupId>
				<artifactId>exec-maven-plugin</artifactId>
				<version>3.4.1</version>
				<executions>
					<execution>
						<id>init-aws</id>
						<phase>process-test-classes</phase>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>bash</executable>
							<arguments>
								<argument>-c</argument>
								<argument>
                                    # Wait for LocalStack container to be ready
                                    sleep 3

                                    # Get LocalStack container ID
                                    CONTAINER_ID=$(docker ps -q --filter "ancestor=localstack/localstack:3.7")

                                    if [ -z "$CONTAINER_ID" ]; then
                                      echo "LocalStack container not found!"
                                      exit 1
                                    fi

                                    echo "Found LocalStack container: $CONTAINER_ID"

                                    # Copy and execute initialization script
                                    docker cp src/test/data/localstack_docker_init.sh $CONTAINER_ID:/tmp/init.sh
                                    docker exec -e BUCKET_NAME=vfs-miniapp-bucket -e KMS_ALIAS=alias/vfs-miniapp-encryption-key -e AWS_DEFAULT_REGION=ap-southeast-1 $CONTAINER_ID bash /tmp/init.sh
								</argument>
							</arguments>
						</configuration>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<configuration>
					<annotationProcessorPaths>
						<path>
							<groupId>org.projectlombok</groupId>
							<artifactId>lombok</artifactId>
						</path>
                        <path>
                            <groupId>org.mapstruct</groupId>
                            <artifactId>mapstruct-processor</artifactId>
                            <version>1.5.5.Final</version>
                        </path>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok-mapstruct-binding</artifactId>
                            <version>0.2.0</version>
                        </path>
					</annotationProcessorPaths>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
				<configuration>
					<excludes>
						<exclude>
							<groupId>org.projectlombok</groupId>
							<artifactId>lombok</artifactId>
						</exclude>
					</excludes>
				</configuration>
			</plugin>
		</plugins>
	</build>

	<repositories>
		<repository>
			<id>vinsf-core-libs-release-reader</id>
			<name>vinsf-core-libs-release-reader-local</name>
			<url>https://gitlab.vinit.tech/api/v4/projects/32/packages/maven</url>
			<layout>default</layout>
		</repository>
	</repositories>

</project>
