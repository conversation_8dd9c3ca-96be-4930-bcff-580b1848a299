---
type: "agent_requested"
description: "Example description"
---

# Performance, Caching & Logging

## Caching Strategy

### Local Cache (Caffeine)
- **Purpose**: Fast in-memory cache for entity lookups within a single instance
- **Implementation**: Initialized in `@PostConstruct` method
- **Configuration**:
  - Default TTL: 15 minutes (`CACHE_TTL_MINUTES`)
  - Default max size: 100 entries (`CACHE_MAX_SIZE`)
  - Eviction policy: Time-based (expireAfterWrite)

```java
@PostConstruct
public void init() {
    idCache = Caffeine.newBuilder()
            .maximumSize(CACHE_MAX_SIZE)
            .expireAfterWrite(CACHE_TTL_MINUTES, TimeUnit.MINUTES)
            .build();

    codeToIdCache = Caffeine.newBuilder()
            .maximumSize(CACHE_MAX_SIZE)
            .expireAfterWrite(CACHE_TTL_MINUTES, TimeUnit.MINUTES)
            .build();

    log.info("Service initialized with Caffeine cache");
}
```

### Cache Structure
- **idCache**: `Cache<String, EntityDto>` - stores full DTO by ID
- **codeToIdCache**: `Cache<String, String>` - maps code to ID (lightweight)
- Cache only simple lookups (id/code), not complex queries or aggregations

### Cache Invalidation
- **Local**: Invalidate via `invalidateCache(Set<String> ids, Set<String> codes)` method
- **Distributed**: Use Redis Pub/Sub to propagate invalidation events across instances
- **Timing**: Publish invalidation events AFTER transaction commit using `TransactionSynchronizationManager`

```java
private void publishCacheInvalidation(String id, String code) {
    if (TransactionSynchronizationManager.isActualTransactionActive()) {
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
            @Override
            public void afterCommit() {
                redisPublisher.sendChangeEvent(Entity.ENTITY_NAME, id, code);
            }
        });
    } else {
        redisPublisher.sendChangeEvent(Entity.ENTITY_NAME, id, code);
    }
}
```

## Database & JPA Optimization

### Entity Design
- **No entity relationships**: Avoid `@OneToMany`, `@ManyToOne`, `@OneToOne`, `@ManyToMany`
- **Use ID references**: Model associations with ID fields (e.g., `categoryId`, `orgCode`)
- **Reason**: Prevents lazy loading issues, N+1 queries, and simplifies caching

### JSON Columns (PostgreSQL)
- Use `@JdbcTypeCode(SqlTypes.JSON)` with `columnDefinition = "jsonb"` for complex data structures
- Examples: `displayNames`, `descriptions`, `logos`, `permissions`, `webhooks`

```java
@JdbcTypeCode(SqlTypes.JSON)
@Column(name = "display_names", columnDefinition = "jsonb")
private Map<String, String> displayNames;

@JdbcTypeCode(SqlTypes.JSON)
@Column(name = "permissions", columnDefinition = "jsonb")
private Set<MiniAppPermission> permissions;
```

### Query Optimization
- **Specifications**: Use JPA Specifications for dynamic queries instead of `@Query` annotations
- **Pagination**: Always use `Pageable` for list operations to limit result sets
- **Projections**: Use DTO projections via MapStruct instead of loading full entities when possible
- **Indexing**: Index columns used in filters (code, status, categoryId, tenantId)

### Transaction Management
- **Write operations**: Use `@Transactional` (default propagation)
- **Read operations**: Use `@Transactional(readOnly = true)` for optimization
- **Cache reads**: Do NOT use `@Transactional` on cache-only methods to avoid overhead
- **Batch operations**: Consider batch size configuration for bulk inserts/updates

## Logging Guidelines

### Log Levels
- **log.info()**: Key lifecycle events
  - Service initialization: `"MiniAppService initialized with Caffeine cache"`
  - Entity creation: `"Creating MiniApp with code: {}"`
  - Entity updates: `"Updating MiniApp with id: {}"`
  - Entity deletion: `"Deleting MiniApp with id: {}"`
  - Success messages: `"MiniApp created successfully with id: {}"`
  - Cache invalidation: `"Invalidating cache for ids: {} and codes: {}"`

- **log.debug()**: Detailed information for troubleshooting
  - Cache lookups: `"Getting MiniApp by id: {}"`
  - Cache hits: `"MiniApp found in cache for id: {}"`
  - Query details: `"Searching MiniApps with filter: {}"`

- **log.warn()**: Unusual but recoverable states
  - Business rule violations: `"MiniApp code already exists: {}"`
  - Entity not found: `"MiniApp not found with id: {}"`
  - Unknown events: `"Unknown entity type: {}"`

- **log.error()**: Execution failures (rare in service layer)
  - Unexpected exceptions
  - System-level errors

### Logging Best Practices
1. **Always use SLF4J via Lombok**: Add `@Slf4j` annotation to classes
2. **Include context**: Always log entity identifiers (id, code) for traceability
3. **Never log sensitive data**: No passwords, tokens, secrets, or PII
4. **Avoid large payloads**: Don't log full DTOs or large collections
5. **Use placeholders**: Use `{}` placeholders instead of string concatenation
6. **Log before throwing**: Use `log.warn()` before throwing business exceptions

## Performance Best Practices

### Service Layer
1. **Cache frequently accessed entities**: Use Caffeine for id/code lookups
2. **Minimize database calls**: Check cache before querying database
3. **Use Optional pattern**: Provide both throwing (`getById`) and Optional (`optById`) methods
4. **Batch operations**: Group multiple operations when possible
5. **Async cache invalidation**: Publish Redis events after transaction commit

### Controller Layer
1. **Validate early**: Use `jakarta.validation` annotations on DTOs
2. **Return minimal DTOs**: Only include necessary fields in responses
3. **Use pagination**: Always paginate list endpoints with `Pageable`
4. **Avoid heavy serialization**: Don't return large nested objects

### MapStruct Optimization
1. **Component model**: Use `componentModel = "spring"` for Spring bean injection
2. **Null handling**: Use `nullValuePropertyMappingStrategy = IGNORE` for partial updates
3. **Ignore audit fields**: Don't map id, code, createdOn, createdBy, version in update methods

## Resilience Patterns

### Error Handling
- Use `BusinessLogicException` for business errors
- Return consistent error structure via global exception handler

### Transaction Safety
- Publish cache invalidation events AFTER transaction commit
- Use `TransactionSynchronizationManager` for post-commit hooks
- Avoid nested transactions unless necessary