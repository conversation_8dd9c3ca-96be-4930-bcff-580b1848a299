#!/usr/bin/env bash
set -euo pipefail

# LocalStack AWS Resource Initialization Script
# This script creates S3 buckets and KMS keys for local development

BUCKET_NAME="${BUCKET_NAME:-vfs-miniapp-bucket}"
KMS_ALIAS="${KMS_ALIAS:-alias/vfs-miniapp-encryption-key}"
AWS_REGION="${AWS_DEFAULT_REGION:-ap-southeast-1}"

echo "=========================================="
echo "LocalStack AWS Resource Initialization"
echo "=========================================="
echo "Bucket Name: ${BUCKET_NAME}"
echo "KMS Alias: ${KMS_ALIAS}"
echo "AWS Region: ${AWS_REGION}"
echo "=========================================="

# Wait for LocalStack to be fully ready
echo "[init] Waiting for LocalStack services to be ready..."
sleep 2

# Create S3 bucket
echo "[init] Checking/creating S3 bucket: ${BUCKET_NAME}"
if awslocal s3api head-bucket --bucket "${BUCKET_NAME}" 2>/dev/null; then
  echo "[init] ✅ S3 bucket already exists: ${BUCKET_NAME}"
else
  echo "[init] 🔄 Creating S3 bucket: ${BUCKET_NAME}"
  awslocal s3 mb "s3://${BUCKET_NAME}" --region "${AWS_REGION}"

  # Set public access block configuration for local testing
  awslocal s3api put-public-access-block \
    --bucket "${BUCKET_NAME}" \
    --public-access-block-configuration BlockPublicAcls=false,IgnorePublicAcls=false,BlockPublicPolicy=false,RestrictPublicBuckets=false

  echo "[init] ✅ Created S3 bucket: ${BUCKET_NAME}"
fi

# Create KMS key and alias
echo "[init] Checking/creating KMS key alias: ${KMS_ALIAS}"
ALIAS_EXISTS=$(awslocal kms list-aliases --query "Aliases[?AliasName=='${KMS_ALIAS}'] | length(@)" --output text)
if [[ "${ALIAS_EXISTS}" -gt 0 ]]; then
  echo "[init] ✅ KMS alias already exists: ${KMS_ALIAS}"
  # Get the key ID for the existing alias
  KEY_ID=$(awslocal kms list-aliases --query "Aliases[?AliasName=='${KMS_ALIAS}'].TargetKeyId" --output text)
  echo "[init] 📋 Existing KMS Key ID: ${KEY_ID}"
else
  echo "[init] 🔄 Creating KMS key and alias: ${KMS_ALIAS}"
  KEY_ID=$(awslocal kms create-key \
    --description "LocalStack MiniApp Service encryption key for local development" \
    --origin INTERNAL \
    --query KeyMetadata.KeyId \
    --output text)

  awslocal kms create-alias --alias-name "${KMS_ALIAS}" --target-key-id "${KEY_ID}"
  echo "[init] ✅ Created KMS key ${KEY_ID} with alias ${KMS_ALIAS}"
fi

# Verify resources were created successfully
echo "=========================================="
echo "Verification: Listing created resources"
echo "=========================================="

echo "[verify] S3 Buckets:"
awslocal s3 ls

echo "[verify] KMS Aliases:"
awslocal kms list-aliases --query "Aliases[?starts_with(AliasName, 'alias/vfs-')]" --output table

echo "=========================================="
echo "✅ LocalStack initialization completed successfully!"
echo "=========================================="
