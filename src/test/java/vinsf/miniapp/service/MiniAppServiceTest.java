package vinsf.miniapp.service;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import vinsf.miniapp.constant.MiniAppStatus;
import vinsf.miniapp.constant.MiniAppType;
import vinsf.miniapp.dto.MiniAppDto;
import vinsf.miniapp.dto.MiniAppFilter;
import vinsf.miniapp.exception.ResourceExistedException;
import vinsf.miniapp.exception.ResourceNotFoundException;
import vinsf.miniapp.mapper.MiniAppMapper;
import vinsf.miniapp.model.MiniApp;
import vinsf.miniapp.publisher.RedisPublisher;
import vinsf.miniapp.repository.MiniAppRepository;
import vinsf.miniapp.service.impl.MiniAppServiceImpl;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * Unit tests for MiniAppService
 *
 * <AUTHOR>
 * @since 2025-09-30
 */
@ExtendWith(MockitoExtension.class)
class MiniAppServiceTest {

    @Mock
    private MiniAppRepository miniAppRepository;

    @Mock
    private MiniAppMapper miniAppMapper;

    @Mock
    private RedisPublisher redisPublisher;

    @InjectMocks
    private MiniAppServiceImpl miniAppService;

    private MiniApp testEntity;
    private MiniAppDto testDto;

    @BeforeEach
    void setUp() {
        Map<String, String> logos = new HashMap<>();
        logos.put("en", "logo-url");
        // Setup test data
        testEntity = new MiniApp();
        testEntity.setId("test-id-123");
        testEntity.setCode("test-code");
        testEntity.setName("Test MiniApp");
        testEntity.setType(MiniAppType.WEB);
        testEntity.setStatus(MiniAppStatus.ACTIVE);
        testEntity.setLogos(logos);
        testEntity.setActive(true);

        testDto = new MiniAppDto(
                "test-id-123",
                "test-code",
                "Test MiniApp",
                null,
                null,
                null,
                MiniAppType.WEB,
                null,
                null,
                logos,
                null,
                Set.of(),
                Set.of(),
                Set.of(),
                Set.of(),
                Set.of(),
                Set.of(),
                Map.of(),
                MiniAppStatus.ACTIVE,
                null, null, null, null, null, true
        );

        // Initialize the service caches
        miniAppService.init();
    }

    @Test
    void create_validDto_success() {
        // Given
        when(miniAppRepository.existsByCode("test-code")).thenReturn(false);
        when(miniAppRepository.save(any(MiniApp.class))).thenReturn(testEntity);

        // When
        MiniApp result = miniAppService.create(testDto);

        // Then
        assertNotNull(result);
        assertEquals("test-code", result.getCode());
        assertEquals("Test MiniApp", result.getName());
        verify(miniAppRepository).existsByCode("test-code");
        verify(miniAppRepository).save(any(MiniApp.class));
        verify(redisPublisher).sendChangeEvent(eq(MiniApp.ENTITY_NAME), eq("test-id-123"), eq("test-code"));
    }

    @Test
    void create_duplicateCode_throwsException() {
        // Given
        when(miniAppRepository.existsByCode("test-code")).thenReturn(true);

        // When & Then
        ResourceExistedException exception = assertThrows(
                ResourceExistedException.class,
                () -> miniAppService.create(testDto)
        );

        assertEquals("MiniApp", exception.getResourceName());
        assertEquals("code", exception.getFieldName());
        assertEquals("test-code", exception.getFieldValue());
        verify(miniAppRepository, never()).save(any());
    }

    @Test
    void getById_existingId_returnsEntity() {
        // Given
        when(miniAppRepository.findById("test-id-123")).thenReturn(Optional.of(testEntity));

        // When
        MiniApp result = miniAppService.getById("test-id-123");

        // Then
        assertNotNull(result);
        assertEquals("test-id-123", result.getId());
        verify(miniAppRepository).findById("test-id-123");
    }

    @Test
    void getById_nonExistingId_throwsException() {
        // Given
        when(miniAppRepository.findById("non-existing")).thenReturn(Optional.empty());

        // When & Then
        ResourceNotFoundException exception = assertThrows(
                ResourceNotFoundException.class,
                () -> miniAppService.getById("non-existing")
        );

        assertEquals("MiniApp", exception.getResourceName());
        assertEquals("id", exception.getFieldName());
        assertEquals("non-existing", exception.getFieldValue());
    }

    @Test
    void getByCode_existingCode_returnsEntity() {
        // Given
        when(miniAppRepository.findByCode("test-code")).thenReturn(Optional.of(testEntity));

        // When
        MiniApp result = miniAppService.getByCode("test-code");

        // Then
        assertNotNull(result);
        assertEquals("test-code", result.getCode());
        verify(miniAppRepository).findByCode("test-code");
    }

    @Test
    void getByCode_nonExistingCode_throwsException() {
        // Given
        when(miniAppRepository.findByCode("non-existing")).thenReturn(Optional.empty());

        // When & Then
        ResourceNotFoundException exception = assertThrows(
                ResourceNotFoundException.class,
                () -> miniAppService.getByCode("non-existing")
        );

        assertEquals("MiniApp", exception.getResourceName());
        assertEquals("code", exception.getFieldName());
        assertEquals("non-existing", exception.getFieldValue());
    }

    @Test
    void getById_cachedEntity_returnsEntityWithoutDatabaseCall() {
        // Given - First call to populate cache
        when(miniAppRepository.findById("test-id-123")).thenReturn(Optional.of(testEntity));
        miniAppService.getById("test-id-123");

        // Reset mock to verify no additional calls
        reset(miniAppRepository);

        // When - Second call should use cache
        MiniApp result = miniAppService.getById("test-id-123");

        // Then
        assertNotNull(result);
        assertEquals("test-id-123", result.getId());
        verify(miniAppRepository, never()).findById(anyString());
    }

    @Test
    void getByCode_cachedCode_returnsEntityWithoutDatabaseCall() {
        // Given - First call to populate cache
        when(miniAppRepository.findByCode("test-code")).thenReturn(Optional.of(testEntity));
        miniAppService.getByCode("test-code");

        // Reset mock to verify no additional calls
        reset(miniAppRepository);

        // When - Second call should use cache
        MiniApp result = miniAppService.getByCode("test-code");

        // Then
        assertNotNull(result);
        assertEquals("test-code", result.getCode());
        verify(miniAppRepository, never()).findByCode(anyString());
    }

    @Test
    void update_existingId_success() {
        // Given
        when(miniAppRepository.findById("test-id-123")).thenReturn(Optional.of(testEntity));
        when(miniAppRepository.save(any(MiniApp.class))).thenReturn(testEntity);

        // When
        MiniApp result = miniAppService.update("test-id-123", testDto);

        // Then
        assertNotNull(result);
        verify(miniAppRepository).save(testEntity);
        verify(redisPublisher).sendChangeEvent(eq(MiniApp.ENTITY_NAME), eq("test-id-123"), eq("test-code"));
    }

    @Test
    void update_nonExistingId_throwsException() {
        // Given
        when(miniAppRepository.findById("non-existing")).thenReturn(Optional.empty());

        // When & Then
        ResourceNotFoundException exception = assertThrows(
                ResourceNotFoundException.class,
                () -> miniAppService.update("non-existing", testDto)
        );

        assertEquals("MiniApp", exception.getResourceName());
        assertEquals("id", exception.getFieldName());
        assertEquals("non-existing", exception.getFieldValue());
        verify(miniAppRepository, never()).save(any());
    }

    @Test
    void updatePartial_existingId_success() {
        // Given
        when(miniAppRepository.findById("test-id-123")).thenReturn(Optional.of(testEntity));
        when(miniAppRepository.save(any(MiniApp.class))).thenReturn(testEntity);

        // When
        MiniApp result = miniAppService.updatePartial("test-id-123", testDto);

        // Then
        assertNotNull(result);
        verify(miniAppRepository).save(testEntity);
        verify(redisPublisher).sendChangeEvent(eq(MiniApp.ENTITY_NAME), eq("test-id-123"), eq("test-code"));
    }

    @Test
    void updatePartial_nonExistingId_throwsException() {
        // Given
        when(miniAppRepository.findById("non-existing")).thenReturn(Optional.empty());

        // When & Then
        ResourceNotFoundException exception = assertThrows(
                ResourceNotFoundException.class,
                () -> miniAppService.updatePartial("non-existing", testDto)
        );

        assertEquals("MiniApp", exception.getResourceName());
        assertEquals("id", exception.getFieldName());
        assertEquals("non-existing", exception.getFieldValue());
        verify(miniAppRepository, never()).save(any());
    }

    @Test
    void delete_existingId_setsActiveToFalse() {
        // Given
        when(miniAppRepository.findById("test-id-123")).thenReturn(Optional.of(testEntity));
        when(miniAppRepository.save(any(MiniApp.class))).thenReturn(testEntity);

        // When
        miniAppService.delete("test-id-123");

        // Then
        ArgumentCaptor<MiniApp> entityCaptor = ArgumentCaptor.forClass(MiniApp.class);
        verify(miniAppRepository).save(entityCaptor.capture());
        MiniApp savedEntity = entityCaptor.getValue();
        assertFalse(savedEntity.getActive());
        verify(redisPublisher).sendChangeEvent(eq(MiniApp.ENTITY_NAME), eq("test-id-123"), eq("test-code"));
    }

    @Test
    void delete_nonExistingId_throwsException() {
        // Given
        when(miniAppRepository.findById("non-existing")).thenReturn(Optional.empty());

        // When & Then
        ResourceNotFoundException exception = assertThrows(
                ResourceNotFoundException.class,
                () -> miniAppService.delete("non-existing")
        );

        assertEquals("MiniApp", exception.getResourceName());
        assertEquals("id", exception.getFieldName());
        assertEquals("non-existing", exception.getFieldValue());
        verify(miniAppRepository, never()).save(any());
    }

    @Test
    void existsByCode_existingCode_returnsTrue() {
        // Given
        when(miniAppRepository.existsByCode("test-code")).thenReturn(true);

        // When
        boolean result = miniAppService.existsByCode("test-code");

        // Then
        assertTrue(result);
        verify(miniAppRepository).existsByCode("test-code");
    }

    @Test
    void existsByCode_nonExistingCode_returnsFalse() {
        // Given
        when(miniAppRepository.existsByCode("non-existing")).thenReturn(false);

        // When
        boolean result = miniAppService.existsByCode("non-existing");

        // Then
        assertFalse(result);
        verify(miniAppRepository).existsByCode("non-existing");
    }

    @Test
    void search_withFilter_returnsPagedResults() {
        // Given
        MiniAppFilter filter = new MiniAppFilter(
                null,
                "test-code",
                "Test",
                1L,
                MiniAppType.WEB,
                MiniAppStatus.ACTIVE
        );
        Pageable pageable = PageRequest.of(0, 10);
        List<MiniApp> entities = List.of(testEntity);
        Page<MiniApp> entityPage = new PageImpl<>(entities, pageable, 1);

        when(miniAppRepository.findAll(any(Specification.class), eq(pageable))).thenReturn(entityPage);

        // When
        Page<MiniApp> result = miniAppService.search(filter, pageable);

        // Then
        assertNotNull(result);
        assertEquals(1, result.getTotalElements());
        assertEquals(1, result.getContent().size());
        assertEquals("test-code", result.getContent().get(0).getCode());
        verify(miniAppRepository).findAll(any(Specification.class), eq(pageable));
    }

    @Test
    void search_withEmptyFilter_returnsAllResults() {
        // Given
        MiniAppFilter emptyFilter = new MiniAppFilter(null, null, null, null, null, null);
        Pageable pageable = PageRequest.of(0, 10);
        List<MiniApp> entities = List.of(testEntity);
        Page<MiniApp> entityPage = new PageImpl<>(entities, pageable, 1);

        when(miniAppRepository.findAll(any(Specification.class), eq(pageable))).thenReturn(entityPage);

        // When
        Page<MiniApp> result = miniAppService.search(emptyFilter, pageable);

        // Then
        assertNotNull(result);
        assertEquals(1, result.getTotalElements());
        verify(miniAppRepository).findAll(any(Specification.class), eq(pageable));
    }

    @Test
    void search_noResults_returnsEmptyPage() {
        // Given
        MiniAppFilter filter = new MiniAppFilter(null, "non-existing", null, null, null, null);
        Pageable pageable = PageRequest.of(0, 10);
        Page<MiniApp> emptyPage = new PageImpl<>(List.of(), pageable, 0);

        when(miniAppRepository.findAll(any(Specification.class), eq(pageable))).thenReturn(emptyPage);

        // When
        Page<MiniApp> result = miniAppService.search(filter, pageable);

        // Then
        assertNotNull(result);
        assertEquals(0, result.getTotalElements());
        assertTrue(result.getContent().isEmpty());
        verify(miniAppRepository).findAll(any(Specification.class), eq(pageable));
    }

    @Test
    void invalidateCache_withIdsAndCodes_invalidatesBothCaches() {
        // Given
        Set<String> ids = Set.of("id1", "id2");
        Set<String> codes = Set.of("code1", "code2");

        // Populate cache first
        when(miniAppRepository.findById("id1")).thenReturn(Optional.of(testEntity));
        miniAppService.getById("id1");

        // When
        miniAppService.invalidateCache(ids, codes);

        // Then - verify cache was invalidated by checking database is called again
        reset(miniAppRepository);
        when(miniAppRepository.findById("id1")).thenReturn(Optional.of(testEntity));
        miniAppService.getById("id1");
        verify(miniAppRepository).findById("id1");
    }

    @Test
    void invalidateCache_emptyIdsAndCodes_doesNotThrowException() {
        // Given
        Set<String> emptyIds = Set.of();
        Set<String> emptyCodes = Set.of();

        // When & Then - should not throw exception
        assertDoesNotThrow(() -> miniAppService.invalidateCache(emptyIds, emptyCodes));
    }

    @Test
    void create_publishesEventAfterTransactionCommit() {
        // Given
        when(miniAppRepository.existsByCode("test-code")).thenReturn(false);
        when(miniAppRepository.save(any(MiniApp.class))).thenReturn(testEntity);

        // When
        miniAppService.create(testDto);

        // Then - verify RedisPublisher is called with correct parameters
        verify(redisPublisher).sendChangeEvent(
                eq(MiniApp.ENTITY_NAME),
                eq("test-id-123"),
                eq("test-code")
        );
    }

    @Test
    void update_publishesEventAfterTransactionCommit() {
        // Given
        when(miniAppRepository.findById("test-id-123")).thenReturn(Optional.of(testEntity));
        when(miniAppRepository.save(any(MiniApp.class))).thenReturn(testEntity);

        // When
        miniAppService.update("test-id-123", testDto);

        // Then - verify RedisPublisher is called with correct parameters
        verify(redisPublisher).sendChangeEvent(
                eq(MiniApp.ENTITY_NAME),
                eq("test-id-123"),
                eq("test-code")
        );
    }

    @Test
    void updatePartial_publishesEventAfterTransactionCommit() {
        // Given
        when(miniAppRepository.findById("test-id-123")).thenReturn(Optional.of(testEntity));
        when(miniAppRepository.save(any(MiniApp.class))).thenReturn(testEntity);

        // When
        miniAppService.updatePartial("test-id-123", testDto);

        // Then - verify RedisPublisher is called with correct parameters
        verify(redisPublisher).sendChangeEvent(
                eq(MiniApp.ENTITY_NAME),
                eq("test-id-123"),
                eq("test-code")
        );
    }

    @Test
    void delete_publishesEventAfterTransactionCommit() {
        // Given
        when(miniAppRepository.findById("test-id-123")).thenReturn(Optional.of(testEntity));
        when(miniAppRepository.save(any(MiniApp.class))).thenReturn(testEntity);

        // When
        miniAppService.delete("test-id-123");

        // Then - verify RedisPublisher is called with correct parameters
        verify(redisPublisher).sendChangeEvent(
                eq(MiniApp.ENTITY_NAME),
                eq("test-id-123"),
                eq("test-code")
        );
    }
}

