package vinsf.miniapp.annotation;

import jakarta.validation.ConstraintValidatorContext;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

/**
 * Unit tests for DomainsValidator
 *
 * <AUTHOR>
 * @since 2025-10-01
 */
@ExtendWith(MockitoExtension.class)
class DomainsValidatorTest {

    @Mock
    private ConstraintValidatorContext context;

    private DomainsValidator validator;

    @BeforeEach
    void setUp() {
        validator = new DomainsValidator();
    }

    @Test
    void isValid_nullValue_returnsTrue() {
        assertTrue(validator.isValid(null, context));
    }

    @Test
    void isValid_validDomains_returnsTrue() {
        assertTrue(validator.isValid("example.com", context));
        assertTrue(validator.isValid("subdomain.example.com", context));
        assertTrue(validator.isValid("api.example.co.uk", context));
        assertTrue(validator.isValid("my-domain.com", context));
        assertTrue(validator.isValid("example123.com", context));
        assertTrue(validator.isValid("123example.com", context));
    }

    @Test
    void isValid_validSubdomains_returnsTrue() {
        assertTrue(validator.isValid("www.example.com", context));
        assertTrue(validator.isValid("api.v1.example.com", context));
        assertTrue(validator.isValid("deep.nested.subdomain.example.com", context));
    }

    @Test
    void isValid_validTLDs_returnsTrue() {
        assertTrue(validator.isValid("example.com", context));
        assertTrue(validator.isValid("example.org", context));
        assertTrue(validator.isValid("example.net", context));
        assertTrue(validator.isValid("example.io", context));
        assertTrue(validator.isValid("example.tech", context));
        assertTrue(validator.isValid("example.co", context));
    }

    @Test
    void isValid_invalidDomainNoTLD_returnsFalse() {
        assertFalse(validator.isValid("example", context));
        assertFalse(validator.isValid("localhost", context));
    }

    @Test
    void isValid_invalidDomainStartsWithDot_returnsFalse() {
        assertFalse(validator.isValid(".example.com", context));
    }

    @Test
    void isValid_invalidDomainEndsWithDot_returnsFalse() {
        assertFalse(validator.isValid("example.com.", context));
    }

    @Test
    void isValid_invalidDomainStartsWithHyphen_returnsFalse() {
        assertFalse(validator.isValid("-example.com", context));
    }

    @Test
    void isValid_invalidDomainEndsWithHyphen_returnsFalse() {
        assertFalse(validator.isValid("example-.com", context));
    }

    @Test
    void isValid_invalidDomainLabelStartsWithHyphen_returnsFalse() {
        assertFalse(validator.isValid("sub.-domain.com", context));
    }

    @Test
    void isValid_invalidDomainLabelEndsWithHyphen_returnsFalse() {
        assertFalse(validator.isValid("subdomain-.example.com", context));
    }

    @Test
    void isValid_invalidDomainTooLong_returnsFalse() {
        String longDomain = "a".repeat(250) + ".com";
        assertFalse(validator.isValid(longDomain, context));
    }

    @Test
    void isValid_invalidDomainLabelTooLong_returnsFalse() {
        String longLabel = "a".repeat(64) + ".com";
        assertFalse(validator.isValid(longLabel, context));
    }

    @Test
    void isValid_invalidDomainEmptyLabel_returnsFalse() {
        assertFalse(validator.isValid("example..com", context));
    }

    @Test
    void isValid_invalidDomainSpecialCharacters_returnsFalse() {
        assertFalse(validator.isValid("exam ple.com", context));
        assertFalse(validator.isValid("exam_ple.com", context));
        assertFalse(validator.isValid("<EMAIL>", context));
        assertFalse(validator.isValid("exam#ple.com", context));
    }

    @Test
    void isValid_invalidTLDNumeric_returnsFalse() {
        assertFalse(validator.isValid("example.123", context));
    }

    @Test
    void isValid_invalidTLDWithHyphen_returnsFalse() {
        assertFalse(validator.isValid("example.co-m", context));
    }

    @Test
    void isValid_emptyString_returnsFalse() {
        assertFalse(validator.isValid("", context));
        assertFalse(validator.isValid("   ", context));
    }

    @Test
    void isValid_whitespaceAroundValidDomain_returnsTrue() {
        assertTrue(validator.isValid("  example.com  ", context));
    }

    @Test
    void isValid_validDomainMaxLabelLength_returnsTrue() {
        String maxLabel = "a".repeat(63) + ".com";
        assertTrue(validator.isValid(maxLabel, context));
    }

    @Test
    void isValid_validDomainWithNumbers_returnsTrue() {
        assertTrue(validator.isValid("api2.example.com", context));
        assertTrue(validator.isValid("v1.api.example.com", context));
        assertTrue(validator.isValid("server123.example.com", context));
    }

    @Test
    void isValid_validInternationalTLD_returnsTrue() {
        assertTrue(validator.isValid("example.technology", context));
        assertTrue(validator.isValid("example.international", context));
    }

    @Test
    void isValid_singleCharacterLabel_returnsTrue() {
        assertTrue(validator.isValid("a.com", context));
        assertTrue(validator.isValid("x.y.com", context));
    }
}

