package vinsf.miniapp.annotation;

import jakarta.validation.ConstraintValidatorContext;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * Unit tests for DomainsCollectionValidator
 *
 * <AUTHOR>
 * @since 2025-10-01
 */
@ExtendWith(MockitoExtension.class)
class DomainsCollectionValidatorTest {

    @Mock
    private ConstraintValidatorContext context;

    @Mock
    private ConstraintValidatorContext.ConstraintViolationBuilder violationBuilder;

    private DomainsCollectionValidator validator;

    @BeforeEach
    void setUp() {
        validator = new DomainsCollectionValidator();

        // Setup mock behavior for constraint violation
        lenient().when(context.buildConstraintViolationWithTemplate(anyString())).thenReturn(violationBuilder);
    }

    @Test
    void isValid_nullCollection_returnsTrue() {
        assertTrue(validator.isValid(null, context));
    }

    @Test
    void isValid_emptyCollection_returnsTrue() {
        assertTrue(validator.isValid(new ArrayList<>(), context));
        assertTrue(validator.isValid(new HashSet<>(), context));
    }

    @Test
    void isValid_validDomainsList_returnsTrue() {
        List<String> validDomains = Arrays.asList(
                "example.com",
                "api.example.com",
                "subdomain.example.org"
        );
        assertTrue(validator.isValid(validDomains, context));
    }

    @Test
    void isValid_validDomainsSet_returnsTrue() {
        Set<String> validDomains = new HashSet<>(Arrays.asList(
                "example.com",
                "api.example.com",
                "test.example.net"
        ));
        assertTrue(validator.isValid(validDomains, context));
    }

    @Test
    void isValid_collectionWithNullValue_returnsTrue() {
        List<String> domainsWithNull = new ArrayList<>();
        domainsWithNull.add("example.com");
        domainsWithNull.add(null);
        domainsWithNull.add("api.example.com");

        assertTrue(validator.isValid(domainsWithNull, context));
    }

    @Test
    void isValid_invalidDomainInCollection_returnsFalse() {
        List<String> invalidDomains = Arrays.asList(
                "example.com",
                "invalid-domain",
                "api.example.com"
        );

        assertFalse(validator.isValid(invalidDomains, context));
        verify(context).disableDefaultConstraintViolation();
        verify(context).buildConstraintViolationWithTemplate(contains("Invalid domain name"));
    }

    @Test
    void isValid_emptyStringInCollection_returnsFalse() {
        List<String> invalidDomains = Arrays.asList(
                "example.com",
                "",
                "api.example.com"
        );

        assertFalse(validator.isValid(invalidDomains, context));
        verify(context).disableDefaultConstraintViolation();
        verify(context).buildConstraintViolationWithTemplate(contains("Empty domain name"));
    }

    @Test
    void isValid_whitespaceOnlyInCollection_returnsFalse() {
        List<String> invalidDomains = Arrays.asList(
                "example.com",
                "   ",
                "api.example.com"
        );

        assertFalse(validator.isValid(invalidDomains, context));
        verify(context).disableDefaultConstraintViolation();
    }

    @Test
    void isValid_domainWithoutTLDInCollection_returnsFalse() {
        List<String> invalidDomains = Arrays.asList(
                "example.com",
                "localhost",
                "api.example.com"
        );

        assertFalse(validator.isValid(invalidDomains, context));
        verify(context).disableDefaultConstraintViolation();
    }

    @Test
    void isValid_domainStartsWithDotInCollection_returnsFalse() {
        List<String> invalidDomains = Arrays.asList(
                "example.com",
                ".example.com",
                "api.example.com"
        );

        assertFalse(validator.isValid(invalidDomains, context));
        verify(context).disableDefaultConstraintViolation();
    }

    @Test
    void isValid_domainEndsWithDotInCollection_returnsFalse() {
        List<String> invalidDomains = Arrays.asList(
                "example.com",
                "example.com.",
                "api.example.com"
        );

        assertFalse(validator.isValid(invalidDomains, context));
        verify(context).disableDefaultConstraintViolation();
    }

    @Test
    void isValid_whitespaceAroundValidDomains_returnsTrue() {
        List<String> validDomains = Arrays.asList(
                "  example.com  ",
                "  api.example.com  ",
                "  test.example.org  "
        );
        assertTrue(validator.isValid(validDomains, context));
    }

    @Test
    void isValid_mixedValidAndInvalidDomains_returnsFalse() {
        List<String> mixedDomains = Arrays.asList(
                "example.com",
                "valid.example.com",
                "invalid_domain.com",
                "another.example.org"
        );

        assertFalse(validator.isValid(mixedDomains, context));
        verify(context).disableDefaultConstraintViolation();
    }

    @Test
    void isValid_multipleSubdomains_returnsTrue() {
        List<String> validDomains = Arrays.asList(
                "api.v1.example.com",
                "cdn.static.example.com",
                "deep.nested.subdomain.example.org"
        );
        assertTrue(validator.isValid(validDomains, context));
    }

    @Test
    void isValid_domainsWithHyphens_returnsTrue() {
        List<String> validDomains = Arrays.asList(
                "my-domain.com",
                "api-v2.example.com",
                "test-subdomain.example.org"
        );
        assertTrue(validator.isValid(validDomains, context));
    }

    @Test
    void isValid_domainsWithNumbers_returnsTrue() {
        List<String> validDomains = Arrays.asList(
                "api2.example.com",
                "server123.example.com",
                "v1.api.example.org"
        );
        assertTrue(validator.isValid(validDomains, context));
    }
}

