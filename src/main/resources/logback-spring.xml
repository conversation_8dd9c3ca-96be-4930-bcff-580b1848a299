<?xml version="1.0" encoding="UTF-8"?>
<configuration scan="false">

    <!-- ====== Props chung ====== -->
    <contextName>vclub-${APP_DIR}</contextName>
    <property name="LOG_PATH" value="logs"/>
    <property name="APP_DIR" value="${spring.application.name}"/>
    <property name="LOG_PATTERN" value="%d{yyyy-MM-dd HH:mm:ss} %-5p %c:%L - [%M] %m%n"/>

    <!-- ====== Appenders ====== -->
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>${LOG_PATTERN}</pattern>
            <charset>utf-8</charset>
        </encoder>
    </appender>

    <appender name="FILEERROR" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_PATH}/${APP_DIR}/${APP_DIR}-error.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.FixedWindowRollingPolicy">
            <fileNamePattern>${LOG_PATH}/${APP_DIR}/${APP_DIR}-error-%i.log</fileNamePattern>
            <minIndex>1</minIndex>
            <maxIndex>4</maxIndex>
        </rollingPolicy>
        <triggeringPolicy class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
            <maxFileSize>50MB</maxFileSize>
        </triggeringPolicy>
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>${LOG_PATTERN}</pattern>
            <charset>utf-8</charset>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>ERROR</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>

    <appender name="FILEWARN" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_PATH}/${APP_DIR}/${APP_DIR}-warn.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.FixedWindowRollingPolicy">
            <fileNamePattern>${LOG_PATH}/${APP_DIR}/${APP_DIR}-warn-%i.log</fileNamePattern>
            <minIndex>1</minIndex>
            <maxIndex>2</maxIndex>
        </rollingPolicy>
        <triggeringPolicy class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
            <maxFileSize>50MB</maxFileSize>
        </triggeringPolicy>
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>${LOG_PATTERN}</pattern>
            <charset>utf-8</charset>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>WARN</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>

    <appender name="FILEINFO" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_PATH}/${APP_DIR}/${APP_DIR}-info.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.FixedWindowRollingPolicy">
            <fileNamePattern>${LOG_PATH}/${APP_DIR}/${APP_DIR}-info-%i.log</fileNamePattern>
            <minIndex>1</minIndex>
            <maxIndex>2</maxIndex>
        </rollingPolicy>
        <triggeringPolicy class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
            <maxFileSize>50MB</maxFileSize>
        </triggeringPolicy>
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>${LOG_PATTERN}</pattern>
            <charset>utf-8</charset>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>INFO</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>

    <appender name="FILEDEBUG" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_PATH}/${APP_DIR}/${APP_DIR}-debug.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.FixedWindowRollingPolicy">
            <fileNamePattern>${LOG_PATH}/${APP_DIR}/${APP_DIR}-debug-%i.log</fileNamePattern>
            <minIndex>1</minIndex>
            <maxIndex>2</maxIndex>
        </rollingPolicy>
        <triggeringPolicy class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
            <maxFileSize>50MB</maxFileSize>
        </triggeringPolicy>
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>${LOG_PATTERN}</pattern>
            <charset>utf-8</charset>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>DEBUG</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>

    <appender name="FILETRACE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_PATH}/${APP_DIR}/${APP_DIR}-trace.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.FixedWindowRollingPolicy">
            <fileNamePattern>${LOG_PATH}/${APP_DIR}/${APP_DIR}-trace-%i.log</fileNamePattern>
            <minIndex>1</minIndex>
            <maxIndex>2</maxIndex>
        </rollingPolicy>
        <triggeringPolicy class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
            <maxFileSize>50MB</maxFileSize>
        </triggeringPolicy>
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>${LOG_PATTERN}</pattern>
            <charset>utf-8</charset>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>TRACE</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>

    <!-- ====== Noise control ====== -->
    <logger name="org.springframework" level="WARN"/>
    <logger name="org.hibernate.SQL_SLOW" level="DEBUG"/>
    <!-- reduce noise from rebalance/consumer  -->
    <logger name="org.apache.kafka.clients.consumer.internals" level="WARN" additivity="false">
        <appender-ref ref="FILEINFO"/>
        <appender-ref ref="CONSOLE"/>
    </logger>
    <logger name="org.apache.kafka.clients.consumer.ConsumerCoordinator" level="WARN" additivity="false">
        <appender-ref ref="FILEINFO"/>
        <appender-ref ref="CONSOLE"/>
    </logger>

    <!-- ====== Root theo profile ====== -->
    <!-- Non-prod: Allow DEBUG/TRACE -->
    <springProfile name="!prod">
        <root level="${LOG_LEVEL:-INFO}">
            <appender-ref ref="FILEERROR"/>
            <appender-ref ref="FILEWARN"/>
            <appender-ref ref="FILEINFO"/>
            <appender-ref ref="FILEDEBUG"/>
            <appender-ref ref="FILETRACE"/>
            <appender-ref ref="CONSOLE"/>
        </root>
    </springProfile>

    <!-- Prod: Only INFO/ERROR/WARN to FILE -->
    <springProfile name="prod">
        <root level="INFO">
            <appender-ref ref="FILEERROR"/>
            <appender-ref ref="FILEWARN"/>
            <appender-ref ref="FILEINFO"/>
            <appender-ref ref="CONSOLE"/>
        </root>
    </springProfile>

</configuration>
