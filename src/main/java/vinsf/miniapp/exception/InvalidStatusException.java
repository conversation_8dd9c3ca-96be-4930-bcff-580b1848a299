package vinsf.miniapp.exception;

import lombok.Getter;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;
import vinsf.miniapp.constant.AppErrorCode;

import java.io.Serial;
import java.util.Arrays;

@Getter
@ResponseStatus(value = HttpStatus.NOT_FOUND)
public class InvalidStatusException extends RuntimeException {

    @Serial
    private static final long serialVersionUID = 1L;

    private final String resourceName;
    private final Object[] expectedStatus;
    private final Object actualStatus;

    public InvalidStatusException(String resourceName, Object[] expectedStatus, Object actualStatus) {
        super();
        this.resourceName = resourceName;
        this.expectedStatus = expectedStatus;
        this.actualStatus = actualStatus;
    }


    public String getMessage() {
        return String.format(AppErrorCode.INVALID_STATUS.getMessage(), resourceName, Arrays.toString(expectedStatus), actualStatus);
    }
}
