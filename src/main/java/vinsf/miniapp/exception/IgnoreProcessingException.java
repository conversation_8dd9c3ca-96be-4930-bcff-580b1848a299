package vinsf.miniapp.exception;

import lombok.Data;
import lombok.EqualsAndHashCode;
import vinsf.miniapp.constant.AppErrorCode;
import vinsf.miniapp.dto.ServiceResponse;
//import vn.vinsf.common.util.JsonUtils;

@Data
@EqualsAndHashCode(callSuper = false)
public class IgnoreProcessingException extends RuntimeException {

    private final ServiceResponse<?> payload;

    public IgnoreProcessingException(ServiceResponse<?> payload) {
//        super(JsonUtils.toString(payload));
        this.payload = payload;
    }

    public IgnoreProcessingException(AppErrorCode errorCode, Object... args) {
        this(ServiceResponse.error(errorCode, args));
    }

}
