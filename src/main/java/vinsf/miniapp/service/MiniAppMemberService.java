package vinsf.miniapp.service;

import jakarta.validation.Valid;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import vinsf.miniapp.dto.MiniAppMemberDto;
import vinsf.miniapp.dto.MiniAppMemberFilter;
import vinsf.miniapp.model.MiniAppMember;

import java.util.List;
import java.util.Optional;
import java.util.Set;

/**
 * Service interface for MiniAppMember entity operations
 *
 * <AUTHOR>
 * @since 2025-10-02
 */
public interface MiniAppMemberService {

    /**
     * Create a new MiniAppMember
     *
     * @param dto the MiniAppMember data
     * @return created MiniAppMember entity
     */
    MiniAppMember create(@Valid MiniAppMemberDto dto);

    /**
     * Get MiniAppMember by ID (cached)
     *
     * @param id the MiniAppMember ID
     * @return MiniAppMember entity
     */
    MiniAppMember getById(Long id);

    /**
     * Get MiniAppMember by miniapp ID and superapp user ID (cached)
     *
     * @param miniappId      the miniapp ID
     * @param superappUserId the superapp user ID
     * @return MiniAppMember entity
     */
    MiniAppMember getByMiniappIdAndSuperappUserId(String miniappId, String superappUserId);

    /**
     * Get MiniAppMember by ID (cached)
     *
     * @param id the MiniAppMember ID
     * @return Optional of MiniAppMember entity
     */
    Optional<MiniAppMember> optById(Long id);

    /**
     * Get MiniAppMember by miniapp ID and superapp user ID (cached)
     *
     * @param miniappId      the miniapp ID
     * @param superappUserId the superapp user ID
     * @return Optional of MiniAppMember entity
     */
    Optional<MiniAppMember> optByMiniappIdAndSuperappUserId(String miniappId, String superappUserId);

    /**
     * Update MiniAppMember
     *
     * @param id  the MiniAppMember ID
     * @param dto the MiniAppMember data
     * @return updated MiniAppMember entity
     */
    MiniAppMember update(Long id, @Valid MiniAppMemberDto dto);

    /**
     * Partially update MiniAppMember
     *
     * @param id  the MiniAppMember ID
     * @param dto the MiniAppMember data (only non-null fields will be updated)
     * @return updated MiniAppMember entity
     */
    MiniAppMember updatePartial(Long id, @Valid MiniAppMemberDto dto);

    /**
     * Delete MiniAppMember (soft delete)
     *
     * @param id the MiniAppMember ID
     */
    void delete(Long id);

    /**
     * Get all MiniAppMembers
     *
     * @return List of MiniAppMember entities
     */
    List<MiniAppMember> getAll();

    /**
     * Get all MiniAppMembers by miniapp ID
     *
     * @param miniappId the miniapp ID
     * @return List of MiniAppMember entities
     */
    List<MiniAppMember> getByMiniappId(String miniappId);

    /**
     * Get all MiniAppMembers by superapp user ID
     *
     * @param superappUserId the superapp user ID
     * @return List of MiniAppMember entities
     */
    List<MiniAppMember> getBySuperappUserId(String superappUserId);

    /**
     * Search MiniAppMembers with filter and pagination
     *
     * @param filter   the filter criteria
     * @param pageable pagination information
     * @return Page of MiniAppMember entities
     */
    Page<MiniAppMember> search(MiniAppMemberFilter filter, Pageable pageable);

    /**
     * Check if MiniAppMember exists by miniapp ID and superapp user ID
     *
     * @param miniappId      the miniapp ID
     * @param superappUserId the superapp user ID
     * @return true if exists
     */
    boolean existsByMiniappIdAndSuperappUserId(String miniappId, String superappUserId);

    /**
     * Invalidate cache for given IDs and miniapp ID + superapp user ID combinations
     *
     * @param ids                           the MiniAppMember IDs
     * @param miniappIdSuperappUserIdPairs  the miniapp ID and superapp user ID pairs
     */
    void invalidateCache(Set<Long> ids, Set<String> miniappIdSuperappUserIdPairs);
}
