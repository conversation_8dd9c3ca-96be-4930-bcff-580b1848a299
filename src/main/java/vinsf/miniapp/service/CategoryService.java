package vinsf.miniapp.service;

import jakarta.validation.Valid;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import vinsf.miniapp.dto.CategoryDto;
import vinsf.miniapp.dto.CategoryFilter;
import vinsf.miniapp.model.Category;

import java.util.List;
import java.util.Optional;
import java.util.Set;

/**
 * Service interface for Category entity operations
 *
 * <AUTHOR>
 * @since 2025-10-02
 */
public interface CategoryService {

    /**
     * Create a new Category
     *
     * @param dto the Category data
     * @return created Category entity
     */
    Category create(@Valid CategoryDto dto);

    /**
     * Get Category by ID (cached)
     *
     * @param id the Category ID
     * @return Category entity
     */
    Category getById(Long id);

    /**
     * Get Category by code (cached)
     *
     * @param code the unique code
     * @return Category entity
     */
    Category getByCode(String code);

    /**
     * Get Category by ID (cached)
     *
     * @param id the Category ID
     * @return Optional of Category entity
     */
    Optional<Category> optById(Long id);

    /**
     * Get Category by code (cached)
     *
     * @param code the unique code
     * @return Optional of Category entity
     */
    Optional<Category> optByCode(String code);

    /**
     * Update Category
     *
     * @param id  the Category ID
     * @param dto the Category data
     * @return updated Category entity
     */
    Category update(Long id, @Valid CategoryDto dto);

    /**
     * Partially update Category
     *
     * @param id  the Category ID
     * @param dto the Category data (only non-null fields will be updated)
     * @return updated Category entity
     */
    Category updatePartial(Long id, @Valid CategoryDto dto);

    /**
     * Delete Category (soft delete)
     *
     * @param id the Category ID
     */
    void delete(Long id);

    /**
     * Get all Categories
     *
     * @return List of Category entities
     */
    List<Category> getAll();

    /**
     * Search Categories with filter and pagination
     *
     * @param filter   the filter criteria
     * @param pageable pagination information
     * @return Page of Category entities
     */
    Page<Category> search(CategoryFilter filter, Pageable pageable);

    /**
     * Check if Category exists by code
     *
     * @param code the unique code
     * @return true if exists
     */
    boolean existsByCode(String code);

    /**
     * Invalidate cache for given IDs and codes
     *
     * @param ids   the Category IDs
     * @param codes the Category codes
     */
    void invalidateCache(Set<Long> ids, Set<String> codes);
}
