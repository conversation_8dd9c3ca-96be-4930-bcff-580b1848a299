package vinsf.miniapp.service;

import jakarta.validation.Valid;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import vinsf.miniapp.constant.ReleaseChannel;
import vinsf.miniapp.dto.MiniAppReleaseDto;
import vinsf.miniapp.dto.MiniAppReleaseFilter;
import vinsf.miniapp.model.MiniAppRelease;

import java.util.List;
import java.util.Optional;
import java.util.Set;

/**
 * Service interface for MiniAppRelease entity operations
 *
 * <AUTHOR>
 * @since 2025-10-02
 */
public interface MiniAppReleaseService {

    /**
     * Create a new MiniAppRelease
     *
     * @param dto the MiniAppRelease data
     * @return created MiniAppRelease entity
     */
    MiniAppRelease create(@Valid MiniAppReleaseDto dto);

    /**
     * Get MiniAppRelease by ID (cached)
     *
     * @param id the MiniAppRelease ID
     * @return MiniAppRelease entity
     */
    MiniAppRelease getById(Long id);

    /**
     * Get MiniAppRelease by miniapp ID and channel (cached)
     *
     * @param miniappId the miniapp ID
     * @param channel   the release channel
     * @return MiniAppRelease entity
     */
    MiniAppRelease getByMiniappIdAndChannel(String miniappId, ReleaseChannel channel);

    /**
     * Get MiniAppRelease by ID (cached)
     *
     * @param id the MiniAppRelease ID
     * @return Optional of MiniAppRelease entity
     */
    Optional<MiniAppRelease> optById(Long id);

    /**
     * Get MiniAppRelease by miniapp ID and channel (cached)
     *
     * @param miniappId the miniapp ID
     * @param channel   the release channel
     * @return Optional of MiniAppRelease entity
     */
    Optional<MiniAppRelease> optByMiniappIdAndChannel(String miniappId, ReleaseChannel channel);

    /**
     * Update MiniAppRelease
     *
     * @param id  the MiniAppRelease ID
     * @param dto the MiniAppRelease data
     * @return updated MiniAppRelease entity
     */
    MiniAppRelease update(Long id, @Valid MiniAppReleaseDto dto);

    /**
     * Partially update MiniAppRelease
     *
     * @param id  the MiniAppRelease ID
     * @param dto the MiniAppRelease data (only non-null fields will be updated)
     * @return updated MiniAppRelease entity
     */
    MiniAppRelease updatePartial(Long id, @Valid MiniAppReleaseDto dto);

    /**
     * Delete MiniAppRelease (soft delete)
     *
     * @param id the MiniAppRelease ID
     */
    void delete(Long id);

    /**
     * Get all MiniAppReleases
     *
     * @return List of MiniAppRelease entities
     */
    List<MiniAppRelease> getAll();

    /**
     * Get all MiniAppReleases by miniapp ID
     *
     * @param miniappId the miniapp ID
     * @return List of MiniAppRelease entities
     */
    List<MiniAppRelease> getByMiniappId(String miniappId);

    /**
     * Get all MiniAppReleases by bundle ID
     *
     * @param bundleId the bundle ID
     * @return List of MiniAppRelease entities
     */
    List<MiniAppRelease> getByBundleId(Long bundleId);

    /**
     * Search MiniAppReleases with filter and pagination
     *
     * @param filter   the filter criteria
     * @param pageable pagination information
     * @return Page of MiniAppRelease entities
     */
    Page<MiniAppRelease> search(MiniAppReleaseFilter filter, Pageable pageable);

    /**
     * Check if MiniAppRelease exists by miniapp ID and channel
     *
     * @param miniappId the miniapp ID
     * @param channel   the release channel
     * @return true if exists
     */
    boolean existsByMiniappIdAndChannel(String miniappId, ReleaseChannel channel);

    /**
     * Invalidate cache for given IDs and miniapp ID + channel combinations
     *
     * @param ids                      the MiniAppRelease IDs
     * @param miniappIdChannelPairs    the miniapp ID and channel pairs
     */
    void invalidateCache(Set<Long> ids, Set<String> miniappIdChannelPairs);
}
