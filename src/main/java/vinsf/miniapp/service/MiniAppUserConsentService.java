package vinsf.miniapp.service;

import jakarta.validation.Valid;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import vinsf.miniapp.dto.MiniAppUserConsentDto;
import vinsf.miniapp.dto.MiniAppUserConsentFilter;
import vinsf.miniapp.model.MiniAppUserConsent;

import java.util.List;
import java.util.Optional;
import java.util.Set;

/**
 * Service interface for MiniAppUserConsent entity operations
 *
 * <AUTHOR>
 * @since 2025-10-02
 */
public interface MiniAppUserConsentService {

    /**
     * Create a new MiniAppUserConsent
     *
     * @param dto the MiniAppUserConsent data
     * @return created MiniAppUserConsent entity
     */
    MiniAppUserConsent create(@Valid MiniAppUserConsentDto dto);

    /**
     * Get MiniAppUserConsent by ID (cached)
     *
     * @param id the MiniAppUserConsent ID
     * @return MiniAppUserConsent entity
     */
    MiniAppUserConsent getById(Long id);

    /**
     * Get MiniAppUserConsent by miniapp ID and miniapp user ID (cached)
     *
     * @param miniappId     the miniapp ID
     * @param miniappUserId the miniapp user ID
     * @return MiniAppUserConsent entity
     */
    MiniAppUserConsent getByMiniappIdAndMiniappUserId(String miniappId, String miniappUserId);

    /**
     * Get MiniAppUserConsent by ID (cached)
     *
     * @param id the MiniAppUserConsent ID
     * @return Optional of MiniAppUserConsent entity
     */
    Optional<MiniAppUserConsent> optById(Long id);

    /**
     * Get MiniAppUserConsent by miniapp ID and miniapp user ID (cached)
     *
     * @param miniappId     the miniapp ID
     * @param miniappUserId the miniapp user ID
     * @return Optional of MiniAppUserConsent entity
     */
    Optional<MiniAppUserConsent> optByMiniappIdAndMiniappUserId(String miniappId, String miniappUserId);

    /**
     * Update MiniAppUserConsent
     *
     * @param id  the MiniAppUserConsent ID
     * @param dto the MiniAppUserConsent data
     * @return updated MiniAppUserConsent entity
     */
    MiniAppUserConsent update(Long id, @Valid MiniAppUserConsentDto dto);

    /**
     * Partially update MiniAppUserConsent
     *
     * @param id  the MiniAppUserConsent ID
     * @param dto the MiniAppUserConsent data (only non-null fields will be updated)
     * @return updated MiniAppUserConsent entity
     */
    MiniAppUserConsent updatePartial(Long id, @Valid MiniAppUserConsentDto dto);

    /**
     * Delete MiniAppUserConsent (soft delete)
     *
     * @param id the MiniAppUserConsent ID
     */
    void delete(Long id);

    /**
     * Get all MiniAppUserConsents
     *
     * @return List of MiniAppUserConsent entities
     */
    List<MiniAppUserConsent> getAll();

    /**
     * Get all MiniAppUserConsents by miniapp ID
     *
     * @param miniappId the miniapp ID
     * @return List of MiniAppUserConsent entities
     */
    List<MiniAppUserConsent> getByMiniappId(String miniappId);

    /**
     * Get all MiniAppUserConsents by miniapp user ID
     *
     * @param miniappUserId the miniapp user ID
     * @return List of MiniAppUserConsent entities
     */
    List<MiniAppUserConsent> getByMiniappUserId(String miniappUserId);

    /**
     * Search MiniAppUserConsents with filter and pagination
     *
     * @param filter   the filter criteria
     * @param pageable pagination information
     * @return Page of MiniAppUserConsent entities
     */
    Page<MiniAppUserConsent> search(MiniAppUserConsentFilter filter, Pageable pageable);

    /**
     * Check if MiniAppUserConsent exists by miniapp ID and miniapp user ID
     *
     * @param miniappId     the miniapp ID
     * @param miniappUserId the miniapp user ID
     * @return true if exists
     */
    boolean existsByMiniappIdAndMiniappUserId(String miniappId, String miniappUserId);

    /**
     * Invalidate cache for given IDs and miniapp ID + miniapp user ID combinations
     *
     * @param ids                           the MiniAppUserConsent IDs
     * @param miniappIdMiniappUserIdPairs   the miniapp ID and miniapp user ID pairs
     */
    void invalidateCache(Set<Long> ids, Set<String> miniappIdMiniappUserIdPairs);
}
