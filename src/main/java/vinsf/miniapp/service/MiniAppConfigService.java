package vinsf.miniapp.service;

import jakarta.validation.Valid;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import vinsf.miniapp.dto.MiniAppConfigDto;
import vinsf.miniapp.dto.MiniAppConfigFilter;
import vinsf.miniapp.model.MiniAppConfig;

import java.util.List;
import java.util.Optional;
import java.util.Set;

/**
 * Service interface for MiniAppConfig entity operations
 *
 * <AUTHOR>
 * @since 2025-10-02
 */
public interface MiniAppConfigService {

    /**
     * Create a new MiniAppConfig
     *
     * @param dto the MiniAppConfig data
     * @return created MiniAppConfig entity
     */
    MiniAppConfig create(@Valid MiniAppConfigDto dto);

    /**
     * Get MiniAppConfig by ID (cached)
     *
     * @param id the MiniAppConfig ID
     * @return MiniAppConfig entity
     */
    MiniAppConfig getById(Long id);

    /**
     * Get MiniAppConfig by miniapp ID (cached)
     *
     * @param miniappId the miniapp ID
     * @return MiniAppConfig entity
     */
    MiniAppConfig getByMiniappId(String miniappId);

    /**
     * Get MiniAppConfig by ID (cached)
     *
     * @param id the MiniAppConfig ID
     * @return Optional of MiniAppConfig entity
     */
    Optional<MiniAppConfig> optById(Long id);

    /**
     * Get MiniAppConfig by miniapp ID (cached)
     *
     * @param miniappId the miniapp ID
     * @return Optional of MiniAppConfig entity
     */
    Optional<MiniAppConfig> optByMiniappId(String miniappId);

    /**
     * Update MiniAppConfig
     *
     * @param id  the MiniAppConfig ID
     * @param dto the MiniAppConfig data
     * @return updated MiniAppConfig entity
     */
    MiniAppConfig update(Long id, @Valid MiniAppConfigDto dto);

    /**
     * Partially update MiniAppConfig
     *
     * @param id  the MiniAppConfig ID
     * @param dto the MiniAppConfig data (only non-null fields will be updated)
     * @return updated MiniAppConfig entity
     */
    MiniAppConfig updatePartial(Long id, @Valid MiniAppConfigDto dto);

    /**
     * Delete MiniAppConfig (soft delete)
     *
     * @param id the MiniAppConfig ID
     */
    void delete(Long id);

    /**
     * Get all MiniAppConfigs
     *
     * @return List of MiniAppConfig entities
     */
    List<MiniAppConfig> getAll();

    /**
     * Search MiniAppConfigs with filter and pagination
     *
     * @param filter   the filter criteria
     * @param pageable pagination information
     * @return Page of MiniAppConfig entities
     */
    Page<MiniAppConfig> search(MiniAppConfigFilter filter, Pageable pageable);

    /**
     * Check if MiniAppConfig exists by miniapp ID
     *
     * @param miniappId the miniapp ID
     * @return true if exists
     */
    boolean existsByMiniappId(String miniappId);

    /**
     * Invalidate cache for given IDs and miniapp IDs
     *
     * @param ids        the MiniAppConfig IDs
     * @param miniappIds the miniapp IDs
     */
    void invalidateCache(Set<Long> ids, Set<String> miniappIds);
}
