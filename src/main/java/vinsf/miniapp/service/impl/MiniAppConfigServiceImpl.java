package vinsf.miniapp.service.impl;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import jakarta.annotation.PostConstruct;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import vinsf.miniapp.dto.MiniAppConfigDto;
import vinsf.miniapp.dto.MiniAppConfigFilter;
import vinsf.miniapp.exception.ResourceNotFoundException;
import vinsf.miniapp.mapper.MiniAppConfigMapper;
import vinsf.miniapp.model.MiniAppConfig;
import vinsf.miniapp.publisher.RedisPublisher;
import vinsf.miniapp.repository.MiniAppConfigRepository;
import vinsf.miniapp.service.MiniAppConfigService;
import vinsf.miniapp.specification.MiniAppConfigSpecification;

import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * Service implementation for MiniAppConfig entity operations
 *
 * <AUTHOR>
 * @since 2025-10-02
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MiniAppConfigServiceImpl implements MiniAppConfigService {
    private static final int CACHE_TTL_MINUTES = 15;
    private static final int CACHE_MAX_SIZE = 100;

    private final MiniAppConfigRepository miniAppConfigRepository;
    private final MiniAppConfigMapper miniAppConfigMapper;
    private final RedisPublisher redisPublisher;

    // Caffeine cache for id -> entity
    private Cache<Long, MiniAppConfig> idCache;
    // Caffeine cache for miniappId -> id mapping
    private Cache<String, Long> miniappIdToIdCache;

    @PostConstruct
    public void init() {
        // Initialize Caffeine caches
        idCache = Caffeine.newBuilder()
                .maximumSize(CACHE_MAX_SIZE)
                .expireAfterWrite(CACHE_TTL_MINUTES, TimeUnit.MINUTES)
                .build();

        miniappIdToIdCache = Caffeine.newBuilder()
                .maximumSize(CACHE_MAX_SIZE)
                .expireAfterWrite(CACHE_TTL_MINUTES, TimeUnit.MINUTES)
                .build();

        log.info("MiniAppConfigService initialized with Caffeine cache");
    }

    @Override
    @Transactional
    public MiniAppConfig create(@Valid MiniAppConfigDto dto) {
        log.info("Creating MiniAppConfig with miniapp ID: {}", dto.miniappId());

        MiniAppConfig entity = miniAppConfigMapper.toEntity(dto);
        MiniAppConfig saved = saveAndPublishChangeEvent(entity);

        log.info("MiniAppConfig created successfully with id: {}", saved.getId());

        return saved;
    }

    @Override
    @Transactional(readOnly = true)
    public MiniAppConfig getById(Long id) {
        return optById(id)
                .orElseThrow(() -> {
                    log.warn("MiniAppConfig not found with id: {}", id);
                    return new ResourceNotFoundException("MiniAppConfig", "id", id);
                });
    }

    @Override
    @Transactional(readOnly = true)
    public MiniAppConfig getByMiniappId(String miniappId) {
        return optByMiniappId(miniappId)
                .orElseThrow(() -> {
                    log.warn("MiniAppConfig not found with miniapp ID: {}", miniappId);
                    return new ResourceNotFoundException("MiniAppConfig", "miniappId", miniappId);
                });
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<MiniAppConfig> optById(Long id) {
        log.debug("Getting MiniAppConfig by id: {}", id);

        // Try to get from cache first
        MiniAppConfig cached = idCache.getIfPresent(id);
        if (cached != null) {
            log.debug("MiniAppConfig found in cache for id: {}", id);
            return Optional.of(cached);
        }

        // Load from database
        Optional<MiniAppConfig> config = miniAppConfigRepository.findById(id);

        config.ifPresent(this::putToCache);

        return config;
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<MiniAppConfig> optByMiniappId(String miniappId) {
        log.debug("Getting MiniAppConfig by miniapp ID: {}", miniappId);

        // Try to get id from cache
        Long cachedId = miniappIdToIdCache.getIfPresent(miniappId);
        if (cachedId != null) {
            log.debug("Found id in cache for miniapp ID: {}", miniappId);
            return optById(cachedId);
        }

        // Load from database
        Optional<MiniAppConfig> config = miniAppConfigRepository.findByMiniappId(miniappId);

        config.ifPresent(this::putToCache);

        return config;
    }

    @Override
    @Transactional
    public MiniAppConfig update(Long id, @Valid MiniAppConfigDto dto) {
        log.info("Updating MiniAppConfig with id: {}", id);

        MiniAppConfig existing = miniAppConfigRepository.findById(id)
                .orElseThrow(() -> {
                    log.warn("MiniAppConfig not found with id: {}", id);
                    return new ResourceNotFoundException("MiniAppConfig", "id", id);
                });

        miniAppConfigMapper.update(existing, dto);
        MiniAppConfig saved = saveAndPublishChangeEvent(existing);

        log.info("MiniAppConfig updated successfully with id: {}", id);

        return saved;
    }

    @Override
    @Transactional
    public MiniAppConfig updatePartial(Long id, @Valid MiniAppConfigDto dto) {
        log.info("Partially updating MiniAppConfig with id: {}", id);

        MiniAppConfig existing = miniAppConfigRepository.findById(id)
                .orElseThrow(() -> {
                    log.warn("MiniAppConfig not found with id: {}", id);
                    return new ResourceNotFoundException("MiniAppConfig", "id", id);
                });

        miniAppConfigMapper.partialUpdate(existing, dto);
        MiniAppConfig saved = saveAndPublishChangeEvent(existing);

        log.info("MiniAppConfig partially updated successfully with id: {}", id);

        return saved;
    }

    @Override
    @Transactional
    public void delete(Long id) {
        log.info("Deleting MiniAppConfig with id: {}", id);

        MiniAppConfig existing = miniAppConfigRepository.findById(id)
                .orElseThrow(() -> {
                    log.warn("MiniAppConfig not found with id: {}", id);
                    return new ResourceNotFoundException("MiniAppConfig", "id", id);
                });

        existing.setActive(false);
        saveAndPublishChangeEvent(existing);

        log.info("MiniAppConfig deleted successfully with id: {}", id);
    }

    @Override
    @Transactional(readOnly = true)
    public List<MiniAppConfig> getAll() {
        log.debug("Getting all MiniAppConfigs");
        return miniAppConfigRepository.findAll();
    }

    @Override
    @Transactional(readOnly = true)
    public Page<MiniAppConfig> search(MiniAppConfigFilter filter, Pageable pageable) {
        log.debug("Searching MiniAppConfigs with filter: {}", filter);

        Specification<MiniAppConfig> spec = MiniAppConfigSpecification.fromFilter(filter);

        return miniAppConfigRepository.findAll(spec, pageable);
    }

    @Override
    @Transactional(readOnly = true)
    public boolean existsByMiniappId(String miniappId) {
        return miniAppConfigRepository.existsByMiniappId(miniappId);
    }

    @Override
    public void invalidateCache(Set<Long> ids, Set<String> miniappIds) {
        log.debug("Invalidating cache for ids: {} and miniapp IDs: {}", ids, miniappIds);

        if (ids != null) {
            ids.forEach(idCache::invalidate);
        }

        if (miniappIds != null) {
            miniappIds.forEach(miniappIdToIdCache::invalidate);
        }
    }

    /**
     * Save entity and publish cache invalidation event
     *
     * @param entity the MiniAppConfig entity
     * @return saved MiniAppConfig entity
     */
    private MiniAppConfig saveAndPublishChangeEvent(MiniAppConfig entity) {
        MiniAppConfig saved = miniAppConfigRepository.save(entity);
        publishCacheInvalidation(saved.getId(), saved.getMiniappId());
        return saved;
    }

    /**
     * Put MiniAppConfig to cache
     *
     * @param config the MiniAppConfig entity
     */
    private void putToCache(MiniAppConfig config) {
        idCache.put(config.getId(), config);
        miniappIdToIdCache.put(config.getMiniappId(), config.getId());
    }

    /**
     * Publish cache invalidation event to Redis Pub/Sub
     *
     * @param id        the MiniAppConfig ID
     * @param miniappId the miniapp ID
     */
    private void publishCacheInvalidation(Long id, String miniappId) {
        if (TransactionSynchronizationManager.isActualTransactionActive()) {
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                @Override
                public void afterCommit() {
                    redisPublisher.sendChangeEvent(MiniAppConfig.ENTITY_NAME, Set.of(id.toString()), Set.of(miniappId));
                }
            });
        } else {
            redisPublisher.sendChangeEvent(MiniAppConfig.ENTITY_NAME, Set.of(id.toString()), Set.of(miniappId));
        }
    }
}
