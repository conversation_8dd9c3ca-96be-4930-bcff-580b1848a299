package vinsf.miniapp.service.impl;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import jakarta.annotation.PostConstruct;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import vinsf.miniapp.dto.CategoryDto;
import vinsf.miniapp.dto.CategoryFilter;
import vinsf.miniapp.exception.ResourceExistedException;
import vinsf.miniapp.exception.ResourceNotFoundException;
import vinsf.miniapp.mapper.CategoryMapper;
import vinsf.miniapp.model.Category;
import vinsf.miniapp.publisher.RedisPublisher;
import vinsf.miniapp.repository.CategoryRepository;
import vinsf.miniapp.service.CategoryService;
import vinsf.miniapp.specification.CategorySpecification;

import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * Service implementation for Category entity operations
 *
 * <AUTHOR>
 * @since 2025-10-02
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CategoryServiceImpl implements CategoryService {
    private static final int CACHE_TTL_MINUTES = 15;
    private static final int CACHE_MAX_SIZE = 100;

    private final CategoryRepository categoryRepository;
    private final CategoryMapper categoryMapper;
    private final RedisPublisher redisPublisher;

    // Caffeine cache for id -> entity
    private Cache<Long, Category> idCache;
    // Caffeine cache for code -> id mapping
    private Cache<String, Long> codeToIdCache;

    @PostConstruct
    public void init() {
        // Initialize Caffeine caches
        idCache = Caffeine.newBuilder()
                .maximumSize(CACHE_MAX_SIZE)
                .expireAfterWrite(CACHE_TTL_MINUTES, TimeUnit.MINUTES)
                .build();

        codeToIdCache = Caffeine.newBuilder()
                .maximumSize(CACHE_MAX_SIZE)
                .expireAfterWrite(CACHE_TTL_MINUTES, TimeUnit.MINUTES)
                .build();

        log.info("CategoryService initialized with Caffeine cache");
    }

    @Override
    @Transactional
    public Category create(@Valid CategoryDto dto) {
        log.info("Creating Category with code: {}", dto.code());

        // Validate code uniqueness
        if (categoryRepository.existsByCode(dto.code())) {
            log.warn("Category code already exists: {}", dto.code());
            throw new ResourceExistedException("Category", "code", dto.code());
        }

        Category entity = categoryMapper.toEntity(dto);
        Category saved = saveAndPublishChangeEvent(entity);

        log.info("Category created successfully with id: {}", saved.getId());

        return saved;
    }

    @Override
    @Transactional(readOnly = true)
    public Category getById(Long id) {
        return optById(id)
                .orElseThrow(() -> {
                    log.warn("Category not found with id: {}", id);
                    return new ResourceNotFoundException("Category", "id", id);
                });
    }

    @Override
    @Transactional(readOnly = true)
    public Category getByCode(String code) {
        return optByCode(code)
                .orElseThrow(() -> {
                    log.warn("Category not found with code: {}", code);
                    return new ResourceNotFoundException("Category", "code", code);
                });
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<Category> optById(Long id) {
        log.debug("Getting Category by id: {}", id);

        // Try to get from cache first
        Category cached = idCache.getIfPresent(id);
        if (cached != null) {
            log.debug("Category found in cache for id: {}", id);
            return Optional.of(cached);
        }

        // Load from database
        Optional<Category> category = categoryRepository.findById(id);

        category.ifPresent(this::putToCache);

        return category;
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<Category> optByCode(String code) {
        log.debug("Getting Category by code: {}", code);

        // Try to get id from code cache
        Long cachedId = codeToIdCache.getIfPresent(code);
        if (cachedId != null) {
            log.debug("Found id in code cache for code: {}", code);
            return optById(cachedId);
        }

        // Load from database
        Optional<Category> category = categoryRepository.findByCode(code);

        category.ifPresent(this::putToCache);

        return category;
    }

    @Override
    @Transactional
    public Category update(Long id, @Valid CategoryDto dto) {
        log.info("Updating Category with id: {}", id);

        Category existing = categoryRepository.findById(id)
                .orElseThrow(() -> {
                    log.warn("Category not found with id: {}", id);
                    return new ResourceNotFoundException("Category", "id", id);
                });

        categoryMapper.update(existing, dto);
        Category saved = saveAndPublishChangeEvent(existing);

        log.info("Category updated successfully with id: {}", id);

        return saved;
    }

    @Override
    @Transactional
    public Category updatePartial(Long id, @Valid CategoryDto dto) {
        log.info("Partially updating Category with id: {}", id);

        Category existing = categoryRepository.findById(id)
                .orElseThrow(() -> {
                    log.warn("Category not found with id: {}", id);
                    return new ResourceNotFoundException("Category", "id", id);
                });

        categoryMapper.partialUpdate(existing, dto);
        Category saved = saveAndPublishChangeEvent(existing);

        log.info("Category partially updated successfully with id: {}", id);

        return saved;
    }

    @Override
    @Transactional
    public void delete(Long id) {
        log.info("Deleting Category with id: {}", id);

        Category existing = categoryRepository.findById(id)
                .orElseThrow(() -> {
                    log.warn("Category not found with id: {}", id);
                    return new ResourceNotFoundException("Category", "id", id);
                });

        existing.setActive(false);
        saveAndPublishChangeEvent(existing);

        log.info("Category deleted successfully with id: {}", id);
    }

    @Override
    @Transactional(readOnly = true)
    public List<Category> getAll() {
        log.debug("Getting all Categories");
        return categoryRepository.findAll();
    }

    @Override
    @Transactional(readOnly = true)
    public Page<Category> search(CategoryFilter filter, Pageable pageable) {
        log.debug("Searching Categories with filter: {}", filter);

        Specification<Category> spec = CategorySpecification.fromFilter(filter);

        return categoryRepository.findAll(spec, pageable);
    }

    @Override
    @Transactional(readOnly = true)
    public boolean existsByCode(String code) {
        return categoryRepository.existsByCode(code);
    }

    @Override
    public void invalidateCache(Set<Long> ids, Set<String> codes) {
        log.debug("Invalidating cache for ids: {} and codes: {}", ids, codes);

        if (ids != null) {
            ids.forEach(idCache::invalidate);
        }

        if (codes != null) {
            codes.forEach(codeToIdCache::invalidate);
        }
    }

    /**
     * Save entity and publish cache invalidation event
     *
     * @param entity the Category entity
     * @return saved Category entity
     */
    private Category saveAndPublishChangeEvent(Category entity) {
        Category saved = categoryRepository.save(entity);
        publishCacheInvalidation(saved.getId(), saved.getCode());
        return saved;
    }

    /**
     * Put Category to cache
     *
     * @param category the Category entity
     */
    private void putToCache(Category category) {
        idCache.put(category.getId(), category);
        codeToIdCache.put(category.getCode(), category.getId());
    }

    /**
     * Publish cache invalidation event to Redis Pub/Sub
     *
     * @param id   the Category ID
     * @param code the Category code
     */
    private void publishCacheInvalidation(Long id, String code) {
        if (TransactionSynchronizationManager.isActualTransactionActive()) {
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                @Override
                public void afterCommit() {
                    redisPublisher.sendChangeEvent(Category.ENTITY_NAME, Set.of(id.toString()), Set.of(code));
                }
            });
        } else {
            redisPublisher.sendChangeEvent(Category.ENTITY_NAME, Set.of(id.toString()), Set.of(code));
        }
    }
}
