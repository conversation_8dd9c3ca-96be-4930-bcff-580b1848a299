package vinsf.miniapp.service.impl;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import jakarta.annotation.PostConstruct;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import vinsf.miniapp.dto.DeveloperDto;
import vinsf.miniapp.dto.DeveloperFilter;
import vinsf.miniapp.exception.ResourceExistedException;
import vinsf.miniapp.exception.ResourceNotFoundException;
import vinsf.miniapp.mapper.DeveloperMapper;
import vinsf.miniapp.model.Developer;
import vinsf.miniapp.publisher.RedisPublisher;
import vinsf.miniapp.repository.DeveloperRepository;
import vinsf.miniapp.service.DeveloperService;
import vinsf.miniapp.specification.DeveloperSpecification;

import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * Service implementation for Developer entity operations
 *
 * <AUTHOR>
 * @since 2025-10-02
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DeveloperServiceImpl implements DeveloperService {
    private static final int CACHE_TTL_MINUTES = 15;
    private static final int CACHE_MAX_SIZE = 100;

    private final DeveloperRepository developerRepository;
    private final DeveloperMapper developerMapper;
    private final RedisPublisher redisPublisher;

    // Caffeine cache for id -> entity
    private Cache<Long, Developer> idCache;
    // Caffeine cache for superappUserId -> id mapping
    private Cache<String, Long> superappUserIdToIdCache;

    @PostConstruct
    public void init() {
        // Initialize Caffeine caches
        idCache = Caffeine.newBuilder()
                .maximumSize(CACHE_MAX_SIZE)
                .expireAfterWrite(CACHE_TTL_MINUTES, TimeUnit.MINUTES)
                .build();

        superappUserIdToIdCache = Caffeine.newBuilder()
                .maximumSize(CACHE_MAX_SIZE)
                .expireAfterWrite(CACHE_TTL_MINUTES, TimeUnit.MINUTES)
                .build();

        log.info("DeveloperService initialized with Caffeine cache");
    }

    @Override
    @Transactional
    public Developer create(@Valid DeveloperDto dto) {
        log.info("Creating Developer with superapp user ID: {}", dto.superappUserId());

        // Validate superapp user ID uniqueness
        if (developerRepository.existsBySuperappUserId(dto.superappUserId())) {
            log.warn("Developer superapp user ID already exists: {}", dto.superappUserId());
            throw new ResourceExistedException("Developer", "superappUserId", dto.superappUserId());
        }

        Developer entity = developerMapper.toEntity(dto);
        Developer saved = saveAndPublishChangeEvent(entity);

        log.info("Developer created successfully with id: {}", saved.getId());

        return saved;
    }

    @Override
    @Transactional(readOnly = true)
    public Developer getById(Long id) {
        return optById(id)
                .orElseThrow(() -> {
                    log.warn("Developer not found with id: {}", id);
                    return new ResourceNotFoundException("Developer", "id", id);
                });
    }

    @Override
    @Transactional(readOnly = true)
    public Developer getBySuperappUserId(String superappUserId) {
        return optBySuperappUserId(superappUserId)
                .orElseThrow(() -> {
                    log.warn("Developer not found with superapp user ID: {}", superappUserId);
                    return new ResourceNotFoundException("Developer", "superappUserId", superappUserId);
                });
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<Developer> optById(Long id) {
        log.debug("Getting Developer by id: {}", id);

        // Try to get from cache first
        Developer cached = idCache.getIfPresent(id);
        if (cached != null) {
            log.debug("Developer found in cache for id: {}", id);
            return Optional.of(cached);
        }

        // Load from database
        Optional<Developer> developer = developerRepository.findById(id);

        developer.ifPresent(this::putToCache);

        return developer;
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<Developer> optBySuperappUserId(String superappUserId) {
        log.debug("Getting Developer by superapp user ID: {}", superappUserId);

        // Try to get id from superapp user ID cache
        Long cachedId = superappUserIdToIdCache.getIfPresent(superappUserId);
        if (cachedId != null) {
            log.debug("Found id in superapp user ID cache for superapp user ID: {}", superappUserId);
            return optById(cachedId);
        }

        // Load from database
        Optional<Developer> developer = developerRepository.findBySuperappUserId(superappUserId);

        developer.ifPresent(this::putToCache);

        return developer;
    }

    @Override
    @Transactional
    public Developer update(Long id, @Valid DeveloperDto dto) {
        log.info("Updating Developer with id: {}", id);

        Developer existing = developerRepository.findById(id)
                .orElseThrow(() -> {
                    log.warn("Developer not found with id: {}", id);
                    return new ResourceNotFoundException("Developer", "id", id);
                });

        developerMapper.update(existing, dto);
        Developer saved = saveAndPublishChangeEvent(existing);

        log.info("Developer updated successfully with id: {}", id);

        return saved;
    }

    @Override
    @Transactional
    public Developer updatePartial(Long id, @Valid DeveloperDto dto) {
        log.info("Partially updating Developer with id: {}", id);

        Developer existing = developerRepository.findById(id)
                .orElseThrow(() -> {
                    log.warn("Developer not found with id: {}", id);
                    return new ResourceNotFoundException("Developer", "id", id);
                });

        developerMapper.partialUpdate(existing, dto);
        Developer saved = saveAndPublishChangeEvent(existing);

        log.info("Developer partially updated successfully with id: {}", id);

        return saved;
    }

    @Override
    @Transactional
    public void delete(Long id) {
        log.info("Deleting Developer with id: {}", id);

        Developer existing = developerRepository.findById(id)
                .orElseThrow(() -> {
                    log.warn("Developer not found with id: {}", id);
                    return new ResourceNotFoundException("Developer", "id", id);
                });

        existing.setActive(false);
        saveAndPublishChangeEvent(existing);

        log.info("Developer deleted successfully with id: {}", id);
    }

    @Override
    @Transactional(readOnly = true)
    public List<Developer> getAll() {
        log.debug("Getting all Developers");
        return developerRepository.findAll();
    }

    @Override
    @Transactional(readOnly = true)
    public Page<Developer> search(DeveloperFilter filter, Pageable pageable) {
        log.debug("Searching Developers with filter: {}", filter);

        Specification<Developer> spec = DeveloperSpecification.fromFilter(filter);

        return developerRepository.findAll(spec, pageable);
    }

    @Override
    @Transactional(readOnly = true)
    public boolean existsBySuperappUserId(String superappUserId) {
        return developerRepository.existsBySuperappUserId(superappUserId);
    }

    @Override
    public void invalidateCache(Set<Long> ids, Set<String> superappUserIds) {
        log.debug("Invalidating cache for ids: {} and superapp user IDs: {}", ids, superappUserIds);

        if (ids != null) {
            ids.forEach(idCache::invalidate);
        }

        if (superappUserIds != null) {
            superappUserIds.forEach(superappUserIdToIdCache::invalidate);
        }
    }

    /**
     * Save entity and publish cache invalidation event
     *
     * @param entity the Developer entity
     * @return saved Developer entity
     */
    private Developer saveAndPublishChangeEvent(Developer entity) {
        Developer saved = developerRepository.save(entity);
        publishCacheInvalidation(saved.getId(), saved.getSuperappUserId());
        return saved;
    }

    /**
     * Put Developer to cache
     *
     * @param developer the Developer entity
     */
    private void putToCache(Developer developer) {
        idCache.put(developer.getId(), developer);
        superappUserIdToIdCache.put(developer.getSuperappUserId(), developer.getId());
    }

    /**
     * Publish cache invalidation event to Redis Pub/Sub
     *
     * @param id             the Developer ID
     * @param superappUserId the superapp user ID
     */
    private void publishCacheInvalidation(Long id, String superappUserId) {
        if (TransactionSynchronizationManager.isActualTransactionActive()) {
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                @Override
                public void afterCommit() {
                    redisPublisher.sendChangeEvent(Developer.ENTITY_NAME, Set.of(id.toString()), Set.of(superappUserId));
                }
            });
        } else {
            redisPublisher.sendChangeEvent(Developer.ENTITY_NAME, Set.of(id.toString()), Set.of(superappUserId));
        }
    }
}
