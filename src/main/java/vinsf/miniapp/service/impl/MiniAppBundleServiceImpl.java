package vinsf.miniapp.service.impl;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import jakarta.annotation.PostConstruct;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import vinsf.miniapp.dto.MiniAppBundleDto;
import vinsf.miniapp.dto.MiniAppBundleFilter;
import vinsf.miniapp.exception.ResourceExistedException;
import vinsf.miniapp.exception.ResourceNotFoundException;
import vinsf.miniapp.mapper.MiniAppBundleMapper;
import vinsf.miniapp.model.MiniAppBundle;
import vinsf.miniapp.publisher.RedisPublisher;
import vinsf.miniapp.repository.MiniAppBundleRepository;
import vinsf.miniapp.service.MiniAppBundleService;
import vinsf.miniapp.specification.MiniAppBundleSpecification;

import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * Service implementation for MiniAppBundle entity operations
 *
 * <AUTHOR>
 * @since 2025-10-02
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MiniAppBundleServiceImpl implements MiniAppBundleService {
    private static final int CACHE_TTL_MINUTES = 15;
    private static final int CACHE_MAX_SIZE = 100;

    private final MiniAppBundleRepository miniAppBundleRepository;
    private final MiniAppBundleMapper miniAppBundleMapper;
    private final RedisPublisher redisPublisher;

    // Caffeine cache for id -> entity
    private Cache<Long, MiniAppBundle> idCache;
    // Caffeine cache for miniappId:semver -> id mapping
    private Cache<String, Long> miniappIdSemverToIdCache;

    @PostConstruct
    public void init() {
        // Initialize Caffeine caches
        idCache = Caffeine.newBuilder()
                .maximumSize(CACHE_MAX_SIZE)
                .expireAfterWrite(CACHE_TTL_MINUTES, TimeUnit.MINUTES)
                .build();

        miniappIdSemverToIdCache = Caffeine.newBuilder()
                .maximumSize(CACHE_MAX_SIZE)
                .expireAfterWrite(CACHE_TTL_MINUTES, TimeUnit.MINUTES)
                .build();

        log.info("MiniAppBundleService initialized with Caffeine cache");
    }

    @Override
    @Transactional
    public MiniAppBundle create(@Valid MiniAppBundleDto dto) {
        log.info("Creating MiniAppBundle with miniapp ID: {} and semver: {}", dto.miniappId(), dto.semver());

        // Validate miniapp ID and semver uniqueness
        if (miniAppBundleRepository.existsByMiniappIdAndSemver(dto.miniappId(), dto.semver())) {
            log.warn("MiniAppBundle already exists for miniapp ID: {} and semver: {}", dto.miniappId(), dto.semver());
            throw new ResourceExistedException("MiniAppBundle", "miniappId:semver", dto.miniappId() + ":" + dto.semver());
        }

        MiniAppBundle entity = miniAppBundleMapper.toEntity(dto);
        MiniAppBundle saved = saveAndPublishChangeEvent(entity);

        log.info("MiniAppBundle created successfully with id: {}", saved.getId());

        return saved;
    }

    @Override
    @Transactional(readOnly = true)
    public MiniAppBundle getById(Long id) {
        return optById(id)
                .orElseThrow(() -> {
                    log.warn("MiniAppBundle not found with id: {}", id);
                    return new ResourceNotFoundException("MiniAppBundle", "id", id);
                });
    }

    @Override
    @Transactional(readOnly = true)
    public MiniAppBundle getByMiniappIdAndSemver(String miniappId, String semver) {
        return optByMiniappIdAndSemver(miniappId, semver)
                .orElseThrow(() -> {
                    log.warn("MiniAppBundle not found with miniapp ID: {} and semver: {}", miniappId, semver);
                    return new ResourceNotFoundException("MiniAppBundle", "miniappId:semver", miniappId + ":" + semver);
                });
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<MiniAppBundle> optById(Long id) {
        log.debug("Getting MiniAppBundle by id: {}", id);

        // Try to get from cache first
        MiniAppBundle cached = idCache.getIfPresent(id);
        if (cached != null) {
            log.debug("MiniAppBundle found in cache for id: {}", id);
            return Optional.of(cached);
        }

        // Load from database
        Optional<MiniAppBundle> bundle = miniAppBundleRepository.findById(id);

        bundle.ifPresent(this::putToCache);

        return bundle;
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<MiniAppBundle> optByMiniappIdAndSemver(String miniappId, String semver) {
        log.debug("Getting MiniAppBundle by miniapp ID: {} and semver: {}", miniappId, semver);

        String cacheKey = miniappId + ":" + semver;
        // Try to get id from cache
        Long cachedId = miniappIdSemverToIdCache.getIfPresent(cacheKey);
        if (cachedId != null) {
            log.debug("Found id in cache for miniapp ID: {} and semver: {}", miniappId, semver);
            return optById(cachedId);
        }

        // Load from database
        Optional<MiniAppBundle> bundle = miniAppBundleRepository.findByMiniappIdAndSemver(miniappId, semver);

        bundle.ifPresent(this::putToCache);

        return bundle;
    }

    @Override
    @Transactional
    public MiniAppBundle update(Long id, @Valid MiniAppBundleDto dto) {
        log.info("Updating MiniAppBundle with id: {}", id);

        MiniAppBundle existing = miniAppBundleRepository.findById(id)
                .orElseThrow(() -> {
                    log.warn("MiniAppBundle not found with id: {}", id);
                    return new ResourceNotFoundException("MiniAppBundle", "id", id);
                });

        miniAppBundleMapper.update(existing, dto);
        MiniAppBundle saved = saveAndPublishChangeEvent(existing);

        log.info("MiniAppBundle updated successfully with id: {}", id);

        return saved;
    }

    @Override
    @Transactional
    public MiniAppBundle updatePartial(Long id, @Valid MiniAppBundleDto dto) {
        log.info("Partially updating MiniAppBundle with id: {}", id);

        MiniAppBundle existing = miniAppBundleRepository.findById(id)
                .orElseThrow(() -> {
                    log.warn("MiniAppBundle not found with id: {}", id);
                    return new ResourceNotFoundException("MiniAppBundle", "id", id);
                });

        miniAppBundleMapper.partialUpdate(existing, dto);
        MiniAppBundle saved = saveAndPublishChangeEvent(existing);

        log.info("MiniAppBundle partially updated successfully with id: {}", id);

        return saved;
    }

    @Override
    @Transactional
    public void delete(Long id) {
        log.info("Deleting MiniAppBundle with id: {}", id);

        MiniAppBundle existing = miniAppBundleRepository.findById(id)
                .orElseThrow(() -> {
                    log.warn("MiniAppBundle not found with id: {}", id);
                    return new ResourceNotFoundException("MiniAppBundle", "id", id);
                });

        existing.setActive(false);
        saveAndPublishChangeEvent(existing);

        log.info("MiniAppBundle deleted successfully with id: {}", id);
    }

    @Override
    @Transactional(readOnly = true)
    public List<MiniAppBundle> getAll() {
        log.debug("Getting all MiniAppBundles");
        return miniAppBundleRepository.findAll();
    }

    @Override
    @Transactional(readOnly = true)
    public List<MiniAppBundle> getByMiniappId(String miniappId) {
        log.debug("Getting MiniAppBundles by miniapp ID: {}", miniappId);
        return miniAppBundleRepository.findByMiniappId(miniappId);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<MiniAppBundle> search(MiniAppBundleFilter filter, Pageable pageable) {
        log.debug("Searching MiniAppBundles with filter: {}", filter);

        Specification<MiniAppBundle> spec = MiniAppBundleSpecification.fromFilter(filter);

        return miniAppBundleRepository.findAll(spec, pageable);
    }

    @Override
    @Transactional(readOnly = true)
    public boolean existsByMiniappIdAndSemver(String miniappId, String semver) {
        return miniAppBundleRepository.existsByMiniappIdAndSemver(miniappId, semver);
    }

    @Override
    public void invalidateCache(Set<Long> ids, Set<String> miniappIdSemverPairs) {
        log.debug("Invalidating cache for ids: {} and miniapp ID:semver pairs: {}", ids, miniappIdSemverPairs);

        if (ids != null) {
            ids.forEach(idCache::invalidate);
        }

        if (miniappIdSemverPairs != null) {
            miniappIdSemverPairs.forEach(miniappIdSemverToIdCache::invalidate);
        }
    }

    /**
     * Save entity and publish cache invalidation event
     *
     * @param entity the MiniAppBundle entity
     * @return saved MiniAppBundle entity
     */
    private MiniAppBundle saveAndPublishChangeEvent(MiniAppBundle entity) {
        MiniAppBundle saved = miniAppBundleRepository.save(entity);
        publishCacheInvalidation(saved.getId(), saved.getMiniappId(), saved.getSemver());
        return saved;
    }

    /**
     * Put MiniAppBundle to cache
     *
     * @param bundle the MiniAppBundle entity
     */
    private void putToCache(MiniAppBundle bundle) {
        idCache.put(bundle.getId(), bundle);
        miniappIdSemverToIdCache.put(bundle.getMiniappId() + ":" + bundle.getSemver(), bundle.getId());
    }

    /**
     * Publish cache invalidation event to Redis Pub/Sub
     *
     * @param id        the MiniAppBundle ID
     * @param miniappId the miniapp ID
     * @param semver    the semantic version
     */
    private void publishCacheInvalidation(Long id, String miniappId, String semver) {
        if (TransactionSynchronizationManager.isActualTransactionActive()) {
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                @Override
                public void afterCommit() {
                    redisPublisher.sendChangeEvent(MiniAppBundle.ENTITY_NAME, Set.of(id.toString()), Set.of(miniappId + ":" + semver));
                }
            });
        } else {
            redisPublisher.sendChangeEvent(MiniAppBundle.ENTITY_NAME, Set.of(id.toString()), Set.of(miniappId + ":" + semver));
        }
    }
}
