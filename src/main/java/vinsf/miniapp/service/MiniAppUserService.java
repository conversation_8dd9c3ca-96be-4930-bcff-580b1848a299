package vinsf.miniapp.service;

import jakarta.validation.Valid;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import vinsf.miniapp.dto.MiniAppUserDto;
import vinsf.miniapp.dto.MiniAppUserFilter;
import vinsf.miniapp.model.MiniAppUser;

import java.util.List;
import java.util.Optional;
import java.util.Set;

/**
 * Service interface for MiniAppUser entity operations
 *
 * <AUTHOR>
 * @since 2025-10-02
 */
public interface MiniAppUserService {

    /**
     * Create a new MiniAppUser
     *
     * @param dto the MiniAppUser data
     * @return created MiniAppUser entity
     */
    MiniAppUser create(@Valid MiniAppUserDto dto);

    /**
     * Get MiniAppUser by ID (cached)
     *
     * @param id the MiniAppUser ID
     * @return MiniAppUser entity
     */
    MiniAppUser getById(String id);

    /**
     * Get MiniAppUser by miniapp ID and superapp user ID (cached)
     *
     * @param miniappId      the miniapp ID
     * @param superappUserId the superapp user ID
     * @return MiniAppUser entity
     */
    MiniAppUser getByMiniappIdAndSuperappUserId(String miniappId, String superappUserId);

    /**
     * Get MiniAppUser by ID (cached)
     *
     * @param id the MiniAppUser ID
     * @return Optional of MiniAppUser entity
     */
    Optional<MiniAppUser> optById(String id);

    /**
     * Get MiniAppUser by miniapp ID and superapp user ID (cached)
     *
     * @param miniappId      the miniapp ID
     * @param superappUserId the superapp user ID
     * @return Optional of MiniAppUser entity
     */
    Optional<MiniAppUser> optByMiniappIdAndSuperappUserId(String miniappId, String superappUserId);

    /**
     * Update MiniAppUser
     *
     * @param id  the MiniAppUser ID
     * @param dto the MiniAppUser data
     * @return updated MiniAppUser entity
     */
    MiniAppUser update(String id, @Valid MiniAppUserDto dto);

    /**
     * Partially update MiniAppUser
     *
     * @param id  the MiniAppUser ID
     * @param dto the MiniAppUser data (only non-null fields will be updated)
     * @return updated MiniAppUser entity
     */
    MiniAppUser updatePartial(String id, @Valid MiniAppUserDto dto);

    /**
     * Delete MiniAppUser (soft delete)
     *
     * @param id the MiniAppUser ID
     */
    void delete(String id);

    /**
     * Get all MiniAppUsers
     *
     * @return List of MiniAppUser entities
     */
    List<MiniAppUser> getAll();

    /**
     * Get all MiniAppUsers by miniapp ID
     *
     * @param miniappId the miniapp ID
     * @return List of MiniAppUser entities
     */
    List<MiniAppUser> getByMiniappId(String miniappId);

    /**
     * Get all MiniAppUsers by superapp user ID
     *
     * @param superappUserId the superapp user ID
     * @return List of MiniAppUser entities
     */
    List<MiniAppUser> getBySuperappUserId(String superappUserId);

    /**
     * Search MiniAppUsers with filter and pagination
     *
     * @param filter   the filter criteria
     * @param pageable pagination information
     * @return Page of MiniAppUser entities
     */
    Page<MiniAppUser> search(MiniAppUserFilter filter, Pageable pageable);

    /**
     * Check if MiniAppUser exists by miniapp ID and superapp user ID
     *
     * @param miniappId      the miniapp ID
     * @param superappUserId the superapp user ID
     * @return true if exists
     */
    boolean existsByMiniappIdAndSuperappUserId(String miniappId, String superappUserId);

    /**
     * Invalidate cache for given IDs and miniapp ID + superapp user ID combinations
     *
     * @param ids                           the MiniAppUser IDs
     * @param miniappIdSuperappUserIdPairs  the miniapp ID and superapp user ID pairs
     */
    void invalidateCache(Set<String> ids, Set<String> miniappIdSuperappUserIdPairs);
}
