package vinsf.miniapp.service;

import jakarta.validation.Valid;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import vinsf.miniapp.dto.MiniAppBundleDto;
import vinsf.miniapp.dto.MiniAppBundleFilter;
import vinsf.miniapp.model.MiniAppBundle;

import java.util.List;
import java.util.Optional;
import java.util.Set;

/**
 * Service interface for MiniAppBundle entity operations
 *
 * <AUTHOR>
 * @since 2025-10-02
 */
public interface MiniAppBundleService {

    /**
     * Create a new MiniAppBundle
     *
     * @param dto the MiniAppBundle data
     * @return created MiniAppBundle entity
     */
    MiniAppBundle create(@Valid MiniAppBundleDto dto);

    /**
     * Get MiniAppBundle by ID (cached)
     *
     * @param id the MiniAppBundle ID
     * @return MiniAppBundle entity
     */
    MiniAppBundle getById(Long id);

    /**
     * Get MiniAppBundle by miniapp ID and semver (cached)
     *
     * @param miniappId the miniapp ID
     * @param semver    the semantic version
     * @return MiniAppBundle entity
     */
    MiniAppBundle getByMiniappIdAndSemver(String miniappId, String semver);

    /**
     * Get MiniAppBundle by ID (cached)
     *
     * @param id the MiniAppBundle ID
     * @return Optional of MiniAppBundle entity
     */
    Optional<MiniAppBundle> optById(Long id);

    /**
     * Get MiniAppBundle by miniapp ID and semver (cached)
     *
     * @param miniappId the miniapp ID
     * @param semver    the semantic version
     * @return Optional of MiniAppBundle entity
     */
    Optional<MiniAppBundle> optByMiniappIdAndSemver(String miniappId, String semver);

    /**
     * Update MiniAppBundle
     *
     * @param id  the MiniAppBundle ID
     * @param dto the MiniAppBundle data
     * @return updated MiniAppBundle entity
     */
    MiniAppBundle update(Long id, @Valid MiniAppBundleDto dto);

    /**
     * Partially update MiniAppBundle
     *
     * @param id  the MiniAppBundle ID
     * @param dto the MiniAppBundle data (only non-null fields will be updated)
     * @return updated MiniAppBundle entity
     */
    MiniAppBundle updatePartial(Long id, @Valid MiniAppBundleDto dto);

    /**
     * Delete MiniAppBundle (soft delete)
     *
     * @param id the MiniAppBundle ID
     */
    void delete(Long id);

    /**
     * Get all MiniAppBundles
     *
     * @return List of MiniAppBundle entities
     */
    List<MiniAppBundle> getAll();

    /**
     * Get all MiniAppBundles by miniapp ID
     *
     * @param miniappId the miniapp ID
     * @return List of MiniAppBundle entities
     */
    List<MiniAppBundle> getByMiniappId(String miniappId);

    /**
     * Search MiniAppBundles with filter and pagination
     *
     * @param filter   the filter criteria
     * @param pageable pagination information
     * @return Page of MiniAppBundle entities
     */
    Page<MiniAppBundle> search(MiniAppBundleFilter filter, Pageable pageable);

    /**
     * Check if MiniAppBundle exists by miniapp ID and semver
     *
     * @param miniappId the miniapp ID
     * @param semver    the semantic version
     * @return true if exists
     */
    boolean existsByMiniappIdAndSemver(String miniappId, String semver);

    /**
     * Invalidate cache for given IDs and miniapp ID + semver combinations
     *
     * @param ids                    the MiniAppBundle IDs
     * @param miniappIdSemverPairs   the miniapp ID and semver pairs
     */
    void invalidateCache(Set<Long> ids, Set<String> miniappIdSemverPairs);
}
