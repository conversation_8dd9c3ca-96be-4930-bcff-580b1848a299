package vinsf.miniapp.service;

import jakarta.validation.Valid;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import vinsf.miniapp.dto.DeveloperDto;
import vinsf.miniapp.dto.DeveloperFilter;
import vinsf.miniapp.model.Developer;

import java.util.List;
import java.util.Optional;
import java.util.Set;

/**
 * Service interface for Developer entity operations
 *
 * <AUTHOR>
 * @since 2025-10-02
 */
public interface DeveloperService {

    /**
     * Create a new Developer
     *
     * @param dto the Developer data
     * @return created Developer entity
     */
    Developer create(@Valid DeveloperDto dto);

    /**
     * Get Developer by ID (cached)
     *
     * @param id the Developer ID
     * @return Developer entity
     */
    Developer getById(Long id);

    /**
     * Get Developer by superapp user ID (cached)
     *
     * @param superappUserId the superapp user ID
     * @return Developer entity
     */
    Developer getBySuperappUserId(String superappUserId);

    /**
     * Get Developer by ID (cached)
     *
     * @param id the Developer ID
     * @return Optional of Developer entity
     */
    Optional<Developer> optById(Long id);

    /**
     * Get Developer by superapp user ID (cached)
     *
     * @param superappUserId the superapp user ID
     * @return Optional of Developer entity
     */
    Optional<Developer> optBySuperappUserId(String superappUserId);

    /**
     * Update Developer
     *
     * @param id  the Developer ID
     * @param dto the Developer data
     * @return updated Developer entity
     */
    Developer update(Long id, @Valid DeveloperDto dto);

    /**
     * Partially update Developer
     *
     * @param id  the Developer ID
     * @param dto the Developer data (only non-null fields will be updated)
     * @return updated Developer entity
     */
    Developer updatePartial(Long id, @Valid DeveloperDto dto);

    /**
     * Delete Developer (soft delete)
     *
     * @param id the Developer ID
     */
    void delete(Long id);

    /**
     * Get all Developers
     *
     * @return List of Developer entities
     */
    List<Developer> getAll();

    /**
     * Search Developers with filter and pagination
     *
     * @param filter   the filter criteria
     * @param pageable pagination information
     * @return Page of Developer entities
     */
    Page<Developer> search(DeveloperFilter filter, Pageable pageable);

    /**
     * Check if Developer exists by superapp user ID
     *
     * @param superappUserId the superapp user ID
     * @return true if exists
     */
    boolean existsBySuperappUserId(String superappUserId);

    /**
     * Invalidate cache for given IDs and superapp user IDs
     *
     * @param ids             the Developer IDs
     * @param superappUserIds the superapp user IDs
     */
    void invalidateCache(Set<Long> ids, Set<String> superappUserIds);
}
