package vinsf.miniapp.service;

import jakarta.validation.Valid;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import vinsf.miniapp.dto.MiniAppUserSessionDto;
import vinsf.miniapp.dto.MiniAppUserSessionFilter;
import vinsf.miniapp.model.MiniAppUserSession;

import java.util.List;
import java.util.Optional;
import java.util.Set;

/**
 * Service interface for MiniAppUserSession entity operations
 *
 * <AUTHOR>
 * @since 2025-10-02
 */
public interface MiniAppUserSessionService {

    /**
     * Create a new MiniAppUserSession
     *
     * @param dto the MiniAppUserSession data
     * @return created MiniAppUserSession entity
     */
    MiniAppUserSession create(@Valid MiniAppUserSessionDto dto);

    /**
     * Get MiniAppUserSession by ID (cached)
     *
     * @param id the MiniAppUserSession ID
     * @return MiniAppUserSession entity
     */
    MiniAppUserSession getById(Long id);

    /**
     * Get MiniAppUserSession by session key (cached)
     *
     * @param sessionKey the session key
     * @return MiniAppUserSession entity
     */
    MiniAppUserSession getBySessionKey(String sessionKey);

    /**
     * Get MiniAppUserSession by ID (cached)
     *
     * @param id the MiniAppUserSession ID
     * @return Optional of MiniAppUserSession entity
     */
    Optional<MiniAppUserSession> optById(Long id);

    /**
     * Get MiniAppUserSession by session key (cached)
     *
     * @param sessionKey the session key
     * @return Optional of MiniAppUserSession entity
     */
    Optional<MiniAppUserSession> optBySessionKey(String sessionKey);

    /**
     * Update MiniAppUserSession
     *
     * @param id  the MiniAppUserSession ID
     * @param dto the MiniAppUserSession data
     * @return updated MiniAppUserSession entity
     */
    MiniAppUserSession update(Long id, @Valid MiniAppUserSessionDto dto);

    /**
     * Partially update MiniAppUserSession
     *
     * @param id  the MiniAppUserSession ID
     * @param dto the MiniAppUserSession data (only non-null fields will be updated)
     * @return updated MiniAppUserSession entity
     */
    MiniAppUserSession updatePartial(Long id, @Valid MiniAppUserSessionDto dto);

    /**
     * Delete MiniAppUserSession (soft delete)
     *
     * @param id the MiniAppUserSession ID
     */
    void delete(Long id);

    /**
     * Get all MiniAppUserSessions
     *
     * @return List of MiniAppUserSession entities
     */
    List<MiniAppUserSession> getAll();

    /**
     * Get all MiniAppUserSessions by miniapp ID
     *
     * @param miniappId the miniapp ID
     * @return List of MiniAppUserSession entities
     */
    List<MiniAppUserSession> getByMiniappId(String miniappId);

    /**
     * Get all MiniAppUserSessions by miniapp user ID
     *
     * @param miniappUserId the miniapp user ID
     * @return List of MiniAppUserSession entities
     */
    List<MiniAppUserSession> getByMiniappUserId(String miniappUserId);

    /**
     * Search MiniAppUserSessions with filter and pagination
     *
     * @param filter   the filter criteria
     * @param pageable pagination information
     * @return Page of MiniAppUserSession entities
     */
    Page<MiniAppUserSession> search(MiniAppUserSessionFilter filter, Pageable pageable);

    /**
     * Check if MiniAppUserSession exists by session key
     *
     * @param sessionKey the session key
     * @return true if exists
     */
    boolean existsBySessionKey(String sessionKey);

    /**
     * Invalidate cache for given IDs and session keys
     *
     * @param ids         the MiniAppUserSession IDs
     * @param sessionKeys the session keys
     */
    void invalidateCache(Set<Long> ids, Set<String> sessionKeys);
}
