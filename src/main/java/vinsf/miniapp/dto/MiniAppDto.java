package vinsf.miniapp.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Builder;
import vinsf.miniapp.annotation.*;
import vinsf.miniapp.constant.*;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.Set;

/**
 * DTO for MiniApp entity
 *
 * <AUTHOR>
 * @since 2025-09-30
 */
@Builder(toBuilder = true)
public record MiniAppDto(
        String id,

        @NotBlank(message = "Code is required")
        @Size(max = 64, message = "Code must not exceed 64 characters")
        String code,

        @NotBlank(message = "Name is required")
        @Size(max = 100, message = "Name must not exceed 100 characters")
        String name,

        @NotNull(message = "Secret is required")
        EncryptedSecret secretEncrypted,

        @NotNull(message = "Category ID is required")
        Long categoryId,

        Long developerId, // đơn vị phát triển (PnL/Partner)

        @NotNull(message = "Type is required")
        MiniAppType type,

        @NotNull(message = "Display names are required")
        @Size(min = 1, message = "Display names must not be empty")
        Map<String, String> displayNames,

        @NotNull(message = "Descriptions are required")
        @Size(min = 1, message = "Descriptions must not be empty")
        @ValidateTextLocaleMap(minLength = 10, maxLength = 500)
        Map<String, String> descriptions,

        @NotNull(message = "Logos are required")
        @Size(min = 1, message = "Logos must not be empty")
        @ValidateImageLocaleMap(supportedTypes = {ImageType.PNG, ImageType.JPG, ImageType.JPEG}, message = "Invalid logo URL in locale map")
        Map<String, String> logos,

        @ValidateImageLocaleMap(supportedTypes = {ImageType.PNG, ImageType.JPG, ImageType.JPEG}, message = "Invalid banner URL in locale map")
        Map<String, String> banners,

        @Size(max = 10, message = "Showcase must not exceed 10 items")
        @ValidateImageUrls(supportedTypes = {ImageType.PNG, ImageType.JPG, ImageType.JPEG}, message = "Invalid showcase image URL")
        Set<String> showcase,

        Set<MiniAppUserScope> scopes,

        Set<MiniAppPermission> permissions,

        @Size(max = 20, message = "Whitelist IPs must not exceed 20 items")
        @ValidateIPs(message = "Whitelist IPs must be valid IPv4, IPv6, or CIDR notation")
        Set<String> whitelistIps,

        @Size(max = 20, message = "Whitelist domains must not exceed 20 items")
        @ValidateDomains(message = "Whitelist domains must be valid domain names")
        Set<String> whitelistDomains,

        Set<MiniAppWebhook> webhooks,

        Map<KeyType, Set<MiniAppKey>> keys,

        @NotNull(message = "Status is required")
        MiniAppStatus status,

        // Audit fields
        Integer version,
        String createdBy,
        String updatedBy,
        LocalDateTime createdOn,
        LocalDateTime updatedOn,
        Boolean active
) {
}

