package vinsf.miniapp.dto;

import jakarta.validation.constraints.NotEmpty;
import lombok.Builder;
import vinsf.miniapp.constant.ConsentStatus;
import vinsf.miniapp.constant.UserScope;

import java.util.Map;

/**
 * <AUTHOR>
 */
@Builder(toBuilder = true)
public record UserScopeConsent(
        UserScope code,
        ConsentStatus status,
        Long grantedAt,
        Long revokeAt,
        Long miniappConfigId
) {
}
