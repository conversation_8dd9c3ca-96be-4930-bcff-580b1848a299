package vinsf.miniapp.dto;

import lombok.Builder;
import vinsf.miniapp.constant.MiniAppStatus;
import vinsf.miniapp.constant.MiniAppType;

/**
 * Filter DTO for MiniApp search/query operations
 *
 * <AUTHOR>
 * @since 2025-09-30
 */
@Builder(toBuilder = true)
public record MiniAppFilter(
        String id,
        String code,
        String name,
        Long categoryId,
        MiniAppType type,
        MiniAppStatus status
) {
}

