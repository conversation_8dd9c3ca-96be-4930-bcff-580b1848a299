package vinsf.miniapp.interceptor;

import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpRequest;
import org.springframework.http.client.ClientHttpRequestExecution;
import org.springframework.http.client.ClientHttpRequestInterceptor;
import org.springframework.http.client.ClientHttpResponse;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.UUID;

/**
 * <AUTHOR>
 * @since 2025-10-02
 */
@Slf4j
public class RestTemplateLoggingInterceptor implements ClientHttpRequestInterceptor {

    @Override
    public ClientHttpResponse intercept(HttpRequest req, byte[] reqBody, ClientHttpRequestExecution ex) throws IOException {

        var uuid = UUID.randomUUID().toString();
        traceRequest(req, reqBody, uuid);

        ClientHttpResponse response = ex.execute(req, reqBody);

        traceResponse(response, uuid);

        return response;
    }

    private void traceRequest(HttpRequest request, byte[] body, String uuid) throws IOException {
        log.debug("""
                               \s
                ===========================Request Begin================================================
                UUID        : {}
                URI         : {}
                Method      : {}
                Headers     : {}
                Request body: {}
                ==========================Request End================================================
               \s""", uuid, request.getURI(), request.getMethod(), request.getHeaders(), new String(body, StandardCharsets.UTF_8));
    }

    private void traceResponse(ClientHttpResponse response, String uuid) throws IOException {
        StringBuilder inputStringBuilder = new StringBuilder();
        BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(response.getBody(), StandardCharsets.UTF_8));
        String line = bufferedReader.readLine();
        while (line != null) {
            inputStringBuilder.append(line);
            inputStringBuilder.append('\n');
            line = bufferedReader.readLine();
        }
        log.debug("""
                               \s
                =============================Response Begin==========================================
                UUID         : {}
                Status code  : {}
                Status text  : {}
                Headers      : {}
                Response body: {}
                =======================Response End=================================================
               \s""", uuid, response.getStatusCode(), response.getStatusText(), response.getHeaders(), inputStringBuilder);

    }
}