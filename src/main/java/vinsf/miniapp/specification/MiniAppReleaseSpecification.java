package vinsf.miniapp.specification;

import jakarta.persistence.criteria.Predicate;
import org.springframework.data.jpa.domain.Specification;
import vinsf.miniapp.dto.MiniAppReleaseFilter;
import vinsf.miniapp.model.MiniAppRelease;

import java.util.ArrayList;
import java.util.List;

/**
 * JPA Specification for dynamic MiniAppRelease queries
 *
 * <AUTHOR>
 * @since 2025-10-02
 */
public class MiniAppReleaseSpecification {

    private MiniAppReleaseSpecification() {
        // Utility class
    }

    /**
     * Build specification from filter DTO
     * Only adds predicates for non-null filter values
     *
     * @param filter the filter criteria
     * @return Specification for MiniAppRelease
     */
    public static Specification<MiniAppRelease> fromFilter(MiniAppReleaseFilter filter) {
        return (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();

            if (filter.id() != null) {
                predicates.add(criteriaBuilder.equal(root.get("id"), filter.id()));
            }

            if (filter.miniappId() != null) {
                predicates.add(criteriaBuilder.equal(root.get("miniappId"), filter.miniappId()));
            }

            if (filter.bundleId() != null) {
                predicates.add(criteriaBuilder.equal(root.get("bundleId"), filter.bundleId()));
            }

            if (filter.channel() != null) {
                predicates.add(criteriaBuilder.equal(root.get("channel"), filter.channel()));
            }

            if (filter.status() != null) {
                predicates.add(criteriaBuilder.equal(root.get("status"), filter.status()));
            }

            if (filter.releaser() != null) {
                predicates.add(criteriaBuilder.like(
                        criteriaBuilder.lower(root.get("releaser")),
                        "%" + filter.releaser().toLowerCase() + "%"
                ));
            }

            if (filter.active() != null) {
                predicates.add(criteriaBuilder.equal(root.get("active"), filter.active()));
            }

            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };
    }
}
