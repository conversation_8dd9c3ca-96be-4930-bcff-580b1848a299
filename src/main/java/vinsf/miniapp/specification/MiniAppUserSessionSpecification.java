package vinsf.miniapp.specification;

import jakarta.persistence.criteria.Predicate;
import org.springframework.data.jpa.domain.Specification;
import vinsf.miniapp.dto.MiniAppUserSessionFilter;
import vinsf.miniapp.model.MiniAppUserSession;

import java.util.ArrayList;
import java.util.List;

/**
 * JPA Specification for dynamic MiniAppUserSession queries
 *
 * <AUTHOR>
 * @since 2025-10-02
 */
public class MiniAppUserSessionSpecification {

    private MiniAppUserSessionSpecification() {
        // Utility class
    }

    /**
     * Build specification from filter DTO
     * Only adds predicates for non-null filter values
     *
     * @param filter the filter criteria
     * @return Specification for MiniAppUserSession
     */
    public static Specification<MiniAppUserSession> fromFilter(MiniAppUserSessionFilter filter) {
        return (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();

            if (filter.id() != null) {
                predicates.add(criteriaBuilder.equal(root.get("id"), filter.id()));
            }

            if (filter.miniappId() != null) {
                predicates.add(criteriaBuilder.equal(root.get("miniappId"), filter.miniappId()));
            }

            if (filter.miniappUserId() != null) {
                predicates.add(criteriaBuilder.equal(root.get("miniappUserId"), filter.miniappUserId()));
            }

            if (filter.sessionKey() != null) {
                predicates.add(criteriaBuilder.equal(root.get("sessionKey"), filter.sessionKey()));
            }

            if (filter.active() != null) {
                predicates.add(criteriaBuilder.equal(root.get("active"), filter.active()));
            }

            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };
    }
}
