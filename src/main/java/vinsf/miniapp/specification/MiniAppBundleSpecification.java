package vinsf.miniapp.specification;

import jakarta.persistence.criteria.Predicate;
import org.springframework.data.jpa.domain.Specification;
import vinsf.miniapp.dto.MiniAppBundleFilter;
import vinsf.miniapp.model.MiniAppBundle;

import java.util.ArrayList;
import java.util.List;

/**
 * JPA Specification for dynamic MiniAppBundle queries
 *
 * <AUTHOR>
 * @since 2025-10-02
 */
public class MiniAppBundleSpecification {

    private MiniAppBundleSpecification() {
        // Utility class
    }

    /**
     * Build specification from filter DTO
     * Only adds predicates for non-null filter values
     *
     * @param filter the filter criteria
     * @return Specification for MiniAppBundle
     */
    public static Specification<MiniAppBundle> fromFilter(MiniAppBundleFilter filter) {
        return (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();

            if (filter.id() != null) {
                predicates.add(criteriaBuilder.equal(root.get("id"), filter.id()));
            }

            if (filter.miniappId() != null) {
                predicates.add(criteriaBuilder.equal(root.get("miniappId"), filter.miniappId()));
            }

            if (filter.semver() != null) {
                predicates.add(criteriaBuilder.equal(root.get("semver"), filter.semver()));
            }

            if (filter.buildNo() != null) {
                predicates.add(criteriaBuilder.equal(root.get("buildNo"), filter.buildNo()));
            }

            if (filter.status() != null) {
                predicates.add(criteriaBuilder.equal(root.get("status"), filter.status()));
            }

            if (filter.uploader() != null) {
                predicates.add(criteriaBuilder.like(
                        criteriaBuilder.lower(root.get("uploader")),
                        "%" + filter.uploader().toLowerCase() + "%"
                ));
            }

            if (filter.submitter() != null) {
                predicates.add(criteriaBuilder.like(
                        criteriaBuilder.lower(root.get("submitter")),
                        "%" + filter.submitter().toLowerCase() + "%"
                ));
            }

            if (filter.reviewer() != null) {
                predicates.add(criteriaBuilder.like(
                        criteriaBuilder.lower(root.get("reviewer")),
                        "%" + filter.reviewer().toLowerCase() + "%"
                ));
            }

            if (filter.active() != null) {
                predicates.add(criteriaBuilder.equal(root.get("active"), filter.active()));
            }

            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };
    }
}
