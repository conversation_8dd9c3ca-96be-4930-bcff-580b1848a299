package vinsf.miniapp.specification;

import jakarta.persistence.criteria.Predicate;
import org.springframework.data.jpa.domain.Specification;
import vinsf.miniapp.dto.MiniAppMemberFilter;
import vinsf.miniapp.model.MiniAppMember;

import java.util.ArrayList;
import java.util.List;

/**
 * JPA Specification for dynamic MiniAppMember queries
 *
 * <AUTHOR>
 * @since 2025-10-02
 */
public class MiniAppMemberSpecification {

    private MiniAppMemberSpecification() {
        // Utility class
    }

    /**
     * Build specification from filter DTO
     * Only adds predicates for non-null filter values
     *
     * @param filter the filter criteria
     * @return Specification for MiniAppMember
     */
    public static Specification<MiniAppMember> fromFilter(MiniAppMemberFilter filter) {
        return (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();

            if (filter.id() != null) {
                predicates.add(criteriaBuilder.equal(root.get("id"), filter.id()));
            }

            if (filter.miniappId() != null) {
                predicates.add(criteriaBuilder.equal(root.get("miniappId"), filter.miniappId()));
            }

            if (filter.superappUserId() != null) {
                predicates.add(criteriaBuilder.equal(root.get("superappUserId"), filter.superappUserId()));
            }

            if (filter.role() != null) {
                predicates.add(criteriaBuilder.equal(root.get("role"), filter.role()));
            }

            if (filter.status() != null) {
                predicates.add(criteriaBuilder.equal(root.get("status"), filter.status()));
            }

            if (filter.active() != null) {
                predicates.add(criteriaBuilder.equal(root.get("active"), filter.active()));
            }

            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };
    }
}
