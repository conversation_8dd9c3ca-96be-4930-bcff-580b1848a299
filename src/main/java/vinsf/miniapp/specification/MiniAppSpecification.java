package vinsf.miniapp.specification;

import jakarta.persistence.criteria.Predicate;
import org.springframework.data.jpa.domain.Specification;
import vinsf.miniapp.dto.MiniAppFilter;
import vinsf.miniapp.model.MiniApp;

import java.util.ArrayList;
import java.util.List;

/**
 * JPA Specification for dynamic MiniApp queries
 *
 * <AUTHOR>
 * @since 2025-09-30
 */
public class MiniAppSpecification {

    private MiniAppSpecification() {
        // Utility class
    }

    /**
     * Build specification from filter DTO
     * Only adds predicates for non-null filter values
     *
     * @param filter the filter criteria
     * @return Specification for MiniApp
     */
    public static Specification<MiniApp> fromFilter(MiniAppFilter filter) {
        return (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();

            if (filter.id() != null) {
                predicates.add(criteriaBuilder.equal(root.get("id"), filter.id()));
            }

            if (filter.code() != null) {
                predicates.add(criteriaBuilder.equal(root.get("code"), filter.code()));
            }

            if (filter.name() != null) {
                predicates.add(criteriaBuilder.like(
                        criteriaBuilder.lower(root.get("name")),
                        "%" + filter.name().toLowerCase() + "%"
                ));
            }

            if (filter.categoryId() != null) {
                predicates.add(criteriaBuilder.equal(root.get("categoryId"), filter.categoryId()));
            }

            if (filter.type() != null) {
                predicates.add(criteriaBuilder.equal(root.get("type"), filter.type()));
            }

            if (filter.status() != null) {
                predicates.add(criteriaBuilder.equal(root.get("status"), filter.status()));
            }

            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };
    }
}

