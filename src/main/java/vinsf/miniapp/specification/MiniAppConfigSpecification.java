package vinsf.miniapp.specification;

import jakarta.persistence.criteria.Predicate;
import org.springframework.data.jpa.domain.Specification;
import vinsf.miniapp.dto.MiniAppConfigFilter;
import vinsf.miniapp.model.MiniAppConfig;

import java.util.ArrayList;
import java.util.List;

/**
 * JPA Specification for dynamic MiniAppConfig queries
 *
 * <AUTHOR>
 * @since 2025-10-02
 */
public class MiniAppConfigSpecification {

    private MiniAppConfigSpecification() {
        // Utility class
    }

    /**
     * Build specification from filter DTO
     * Only adds predicates for non-null filter values
     *
     * @param filter the filter criteria
     * @return Specification for MiniAppConfig
     */
    public static Specification<MiniAppConfig> fromFilter(MiniAppConfigFilter filter) {
        return (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();

            if (filter.id() != null) {
                predicates.add(criteriaBuilder.equal(root.get("id"), filter.id()));
            }

            if (filter.miniappId() != null) {
                predicates.add(criteriaBuilder.equal(root.get("miniappId"), filter.miniappId()));
            }

            if (filter.active() != null) {
                predicates.add(criteriaBuilder.equal(root.get("active"), filter.active()));
            }

            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };
    }
}
