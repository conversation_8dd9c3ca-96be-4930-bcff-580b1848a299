package vinsf.miniapp.annotation;

import org.hibernate.annotations.IdGeneratorType;

import java.lang.annotation.Retention;
import java.lang.annotation.Target;

import static java.lang.annotation.ElementType.FIELD;
import static java.lang.annotation.ElementType.METHOD;
import static java.lang.annotation.RetentionPolicy.RUNTIME;


@IdGeneratorType(UUIDv7Generator.class)
@Target({FIELD, METHOD})
@Retention(RUNTIME)
public @interface UUIDv7Generated {}