package vinsf.miniapp.annotation;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;

import java.lang.annotation.*;

/**
 * Validates single IP address (String) or collection of IP addresses (List&lt;String&gt;, Set&lt;String&gt;, etc.).
 * Supports IPv4, IPv6, and CIDR notation for both.
 *
 * <p>Supported formats:</p>
 * <ul>
 *   <li>Single IPv4 address (e.g., "***********")</li>
 *   <li>Single IPv6 address (e.g., "2001:0db8:85a3::8a2e:0370:7334")</li>
 *   <li>CIDR notation for IPv4 (e.g., "***********/24")</li>
 *   <li>CIDR notation for IPv6 (e.g., "2001:0db8::/32")</li>
 * </ul>
 *
 * <AUTHOR>
 * @since 2025-10-01
 */
@Documented
@Constraint(validatedBy = {IPsValidator.class, IPsCollectionValidator.class})
@Target({ElementType.FIELD, ElementType.PARAMETER})
@Retention(RetentionPolicy.RUNTIME)
public @interface ValidateIPs {

    /**
     * Error message when validation fails
     */
    String message() default "Invalid IP address or CIDR notation";

    /**
     * Validation groups
     */
    Class<?>[] groups() default {};

    /**
     * Payload for clients
     */
    Class<? extends Payload>[] payload() default {};
}

