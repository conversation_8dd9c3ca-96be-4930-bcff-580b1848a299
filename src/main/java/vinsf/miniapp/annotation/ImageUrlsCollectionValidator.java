package vinsf.miniapp.annotation;

import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import vinsf.miniapp.constant.ImageType;
import vinsf.miniapp.util.CStringUtil;

import java.util.Arrays;
import java.util.Collection;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * Validator for {@link ValidateImageUrls} annotation for collection of image URLs.
 * Validates that each URL in the collection is a valid HTTP/HTTPS URL with a supported image file extension.
 *
 * <AUTHOR>
 * @since 2025-10-01
 */
public class ImageUrlsCollectionValidator implements ConstraintValidator<ValidateImageUrls, Collection<String>> {

    private Set<ImageType> supportedTypes;

    @Override
    public void initialize(ValidateImageUrls constraintAnnotation) {
        // Convert supported types to lowercase for case-insensitive comparison
        this.supportedTypes = Arrays.stream(constraintAnnotation.supportedTypes())
                .collect(Collectors.toSet());
    }

    @Override
    public boolean isValid(Collection<String> value, ConstraintValidatorContext context) {
        // Null values are considered valid (use @NotNull separately if needed)
        if (value == null) {
            return true;
        }

        // Validate each URL in the collection
        for (String imageUrl : value) {
            // Skip null values
            if (imageUrl == null) {
                continue;
            }

            String trimmedUrl = imageUrl.trim();
            if (trimmedUrl.isEmpty()) {
                context.disableDefaultConstraintViolation();
                context.buildConstraintViolationWithTemplate(
                        "Empty image URL found in collection"
                ).addConstraintViolation();
                return false;
            }

            if (!CStringUtil.isValidImageUrl(trimmedUrl, supportedTypes)) {
                context.disableDefaultConstraintViolation();
                context.buildConstraintViolationWithTemplate(
                        String.format("Invalid image URL: %s. Must be HTTP/HTTPS URL with supported extension: %s",
                                imageUrl, supportedTypes.stream().map(ImageType::getExt).collect(Collectors.joining(", ")))
                ).addConstraintViolation();
                return false;
            }
        }

        return true;
    }
}

