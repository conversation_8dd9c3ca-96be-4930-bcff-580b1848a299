package vinsf.miniapp.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.StringUtils;
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials;
import software.amazon.awssdk.auth.credentials.DefaultCredentialsProvider;
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.kms.KmsClient;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.S3Configuration;

import java.net.URI;

@Configuration
public class AwsClientsConfig {

    @Value("${aws.region:}")
    private String region;

    @Value("${aws.localstack.endpoint:}")
    private String localstackEndpoint;

    @Value("${aws.s3.forcePathStyle:false}")
    private boolean s3ForcePathStyle;

    @Value("${aws.localstack.accessKey:}")
    private String localstackAccessKey;

    @Value("${aws.localstack.secretKey:}")
    private String localstackSecretKey;

    @Bean(destroyMethod = "close")
    KmsClient kmsClient() {
        var b = KmsClient.builder().region(Region.of(region));

        if (StringUtils.hasText(localstackAccessKey) && StringUtils.hasText(localstackSecretKey)) {
            b.credentialsProvider(StaticCredentialsProvider.create(
                    AwsBasicCredentials.create(localstackAccessKey, localstackSecretKey)
            ));
        } else {
            b.credentialsProvider(DefaultCredentialsProvider.create());
        }

        if (StringUtils.hasText(localstackEndpoint)) {
            b.endpointOverride(URI.create(localstackEndpoint));
        }
        return b.build();
    }

    @Bean(destroyMethod = "close")
    S3Client s3Client() {
        var builder = S3Client.builder()
                .region(Region.of(region));

        if (StringUtils.hasText(localstackAccessKey) && StringUtils.hasText(localstackSecretKey)) {
            builder.credentialsProvider(() ->
                    AwsBasicCredentials.create(localstackAccessKey, localstackSecretKey)
            );
        } else {
            builder.credentialsProvider(DefaultCredentialsProvider.create());
        }

        if (StringUtils.hasText(localstackEndpoint)) {
            builder.endpointOverride(URI.create(localstackEndpoint))
                    .serviceConfiguration(S3Configuration.builder()
                            .pathStyleAccessEnabled(s3ForcePathStyle)
                            .build());
        }

        return builder.build();
    }
}
