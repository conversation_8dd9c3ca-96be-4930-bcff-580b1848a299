package vinsf.miniapp.config;

import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.config.Config;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class RedissonConfig {
    @Value("${redisson.config.address}")
    private String redissonAddress;

    @Value("${redisson.config.password:}")
    private String redissonPassword;

    @Value("${redisson.config.connection.max:64}")
    private int connectionMaxPoolSize;

    @Value("${redisson.config.connection.min:24}")
    private int connectionCorePoolSize;

    @Value("${redisson.config.connection.timeout:3000}")
    private int connectionTimeoutMs;

    @Value("${redisson.config.response.timeout:3000}")
    private int responseTimeoutMs;

    @Value("${redisson.config.database:0}")
    private int database;

    @Value("${redisson.config.connection.idle.time:0}")
    private int connectionIdleTimeMs;

    @Bean
    public RedissonClient redissonClient() {
        Config config = new Config();
        config.useSingleServer()
                .setConnectionPoolSize(connectionMaxPoolSize)
                .setConnectionMinimumIdleSize(connectionCorePoolSize)
                .setIdleConnectionTimeout(connectionIdleTimeMs)
                .setConnectTimeout(connectionTimeoutMs)
                .setTimeout(responseTimeoutMs)
                .setDatabase(database)
                .setKeepAlive(true)
                .setAddress(redissonAddress)
                .setPassword(redissonPassword.isEmpty() ? null : redissonPassword);
        return Redisson.create(config);
    }
}
