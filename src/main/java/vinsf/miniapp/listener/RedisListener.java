package vinsf.miniapp.listener;

import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.convert.ReadingConverter;
import org.springframework.stereotype.Component;
import vinsf.miniapp.dto.ChangeEvent;
import vinsf.miniapp.model.MiniApp;
import vinsf.miniapp.service.MiniAppService;
import vinsf.miniapp.util.JsonUtil;

@Component
@Slf4j
@ReadingConverter
@RequiredArgsConstructor
public class RedisListener {
    @Value("${redis.channel.change-event}")
    private String changeEventChannel;

    private final RedissonClient redissonClient;

    private final JsonUtil jsonUtil;

    private final MiniAppService miniAppService;

    @PostConstruct
    public void init() {
        redissonClient.getTopic(changeEventChannel).addListener(String.class, (channel, msg) -> {
            log.info("Received change event message: {}", msg);
            var data = jsonUtil.fromJson(msg, ChangeEvent.class);
            switch (data.entity()) {
                case MiniApp.ENTITY_NAME -> miniAppService.invalidateCache(data.ids(), data.codes());
                default -> log.warn("Unknown entity type: {}", data.entity());
            }

        });
    }
}
