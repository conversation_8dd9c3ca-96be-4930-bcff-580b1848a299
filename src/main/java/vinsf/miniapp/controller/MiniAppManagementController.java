package vinsf.miniapp.controller;


import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.web.bind.annotation.*;
import vinsf.miniapp.dto.*;
import vinsf.miniapp.service.MiniAppManagementService;

@RestController
@RequestMapping("/mms/v1")
@RequiredArgsConstructor
public class MiniAppManagementController {
    private final MiniAppManagementService miniAppManagementService;

    @PostMapping("/miniapps")
    public ServiceResponse<MiniAppDto> create(@RequestBody @Valid MiniAppCreateDto dto) {
        return ServiceResponse.success(miniAppManagementService.create(dto));
    }

    @GetMapping("/miniapps")
    public ServiceResponse<Page<MiniAppDto>> search(MiniAppFilter filter, Pageable pageable) {
        return ServiceResponse.success(miniAppManagementService.search(filter, pageable));
    }

    @GetMapping("/miniapps/{id}")
    public ServiceResponse<MiniAppDto> getById(@PathVariable String id) {
        return ServiceResponse.success(miniAppManagementService.getById(id));
    }

    @PutMapping("/miniapps/{id}")
    public ServiceResponse<MiniAppDto> update(
            @PathVariable String id,
            @RequestBody @Valid MiniAppUpdateDto dto) {
        return ServiceResponse.success(miniAppManagementService.update(id, dto));
    }

    @DeleteMapping("/miniapps/{id}")
    public ServiceResponse<Boolean> delete(@PathVariable String id) {
        miniAppManagementService.delete(id);
        return ServiceResponse.success(true);
    }

    @GetMapping("/miniapps/{id}/secret")
    public ServiceResponse<String> getSecret(@PathVariable String id) {
        return ServiceResponse.success(miniAppManagementService.retrieveSecret(id));
    }

    @PutMapping("/miniapps/{id}/secret")
    public ServiceResponse<MiniAppDto> regenerateSecret(@PathVariable String id) {
        return ServiceResponse.success(miniAppManagementService.regenerateSecret(id));
    }
}
