package vinsf.miniapp.controller;


import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import vinsf.miniapp.dto.ServiceResponse;

import java.util.Map;

@RestController
@RequestMapping("/hello")
@RequiredArgsConstructor
public class HelloController {

    @GetMapping("/world")
    public ServiceResponse<String> helloWorld() {
        return ServiceResponse.success("Hello, World!");
    }

    @GetMapping("/world/json")
    public ServiceResponse<?> helloWorldJsonStr() {
        return ServiceResponse.success(Map.of("first", "hello", "second", "world"));
    }

}
