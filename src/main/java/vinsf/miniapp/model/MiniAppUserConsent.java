package vinsf.miniapp.model;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldNameConstants;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;
import vinsf.miniapp.constant.UserScope;
import vinsf.miniapp.dto.UserScopeConsent;
import vinsf.miniapp.model.base.BaseEntityLong;
import vinsf.miniapp.model.base.BaseEntityUUID;

import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Entity
@Table(name = "miniapp_user_consents")
@FieldNameConstants
public class MiniAppUserConsent extends BaseEntityLong {
    public static final String ENTITY_NAME = "MINIAPP_USER_CONSENT";

    @Column(name = "miniapp_id", nullable = false, updatable = false)
    private String miniappId;

    @Column(name = "miniapp_user_id", nullable = false, updatable = false)
    private String miniappUserId;

    @Column(name = "consented_scopes", columnDefinition = "jsonb")
    @JdbcTypeCode(SqlTypes.JSON)
    private Map<UserScope, UserScopeConsent> consentedScopes;


    @Column(name = "consent_histories", columnDefinition = "jsonb")
    @JdbcTypeCode(SqlTypes.JSON)
    private Map<Long, Set<UserScopeConsent>> consentHistories;
}
