package vinsf.miniapp.model;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldNameConstants;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;
import vinsf.miniapp.constant.DeveloperStatus;
import vinsf.miniapp.constant.DeveloperType;
import vinsf.miniapp.model.base.BaseEntityLong;

import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Entity
@Table(name = "developers")
@FieldNameConstants
public class Developer extends BaseEntityLong {
    public static final String ENTITY_NAME = "DEVELOPER";

    @Column(name = "superapp_user_id", nullable = false, updatable = false)
    private String superappUserId;

    @Column(name = "type", length = 16, nullable = false)
    @Enumerated(EnumType.STRING)
    private DeveloperType type;

    @Column(name = "individual_name")
    private String individualName;

    @Column(name = "company_name")
    private String companyName;

    @Column(name = "company_address")
    private String companyAddress;

    @Column(name = "company_logo")
    private String companyLogo;

    @Column(name = "company_website")
    private String companyWebsite;

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "business_fields", columnDefinition = "jsonb")
    private Set<String> businessFields;

    @Column(name = "tax_code")
    private String taxCode;

    @Column(name = "contact_name")
    private String contactName;

    @Column(name = "contact_email")
    private String contactEmail;

    @Column(name = "contact_phone")
    private String contactPhone;

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "metadata", columnDefinition = "jsonb")
    private Map<String, Object> metadata;

    @Column(name = "status", length = 16)
    @Enumerated(EnumType.STRING)
    private DeveloperStatus status;
}
