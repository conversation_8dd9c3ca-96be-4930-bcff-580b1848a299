package vinsf.miniapp.model;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldNameConstants;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;
import vinsf.miniapp.model.base.BaseEntityLong;

import java.util.Map;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Entity
@Table(name = "categories")
@FieldNameConstants
public class Category extends BaseEntityLong {
    public static final String ENTITY_NAME = "CATEGORY";

    @Column(name = "code", length = 64, nullable = false, updatable = false)
    private String code;

    @Column(name = "name", length = 100, nullable = false)
    private String name;

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "display_names", columnDefinition = "jsonb")
    private Map<String, String> displayNames; // {"vi": "...", "en":"..."}

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "descriptions", columnDefinition = "jsonb")
    private Map<String, String> descriptions; // {"vi": "...", "en":"..."}

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "logos", columnDefinition = "jsonb")
    private Map<String, String> logos; // {"vi": "...", "en":"..."}

    @Column(name = "parent_id")
    private Long parentId;

}
