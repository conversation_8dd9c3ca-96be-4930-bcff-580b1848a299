package vinsf.miniapp.model;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldNameConstants;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;
import vinsf.miniapp.constant.MiniAppPermission;
import vinsf.miniapp.dto.MiniAppUserScope;
import vinsf.miniapp.model.base.BaseEntityLong;

import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Entity
@Table(name = "miniapp_configs")
@FieldNameConstants
public class MiniAppConfig extends BaseEntityLong {
    public static final String ENTITY_NAME = "MINIAPP_CONFIGS";

    @Column(name = "miniapp_id", nullable = false)
    private String miniappId;

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "display_names", columnDefinition = "jsonb")
    private Map<String, String> displayNames; // {"vi": "...", "en":"..."}

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "descriptions", columnDefinition = "jsonb")
    private Map<String, String> descriptions; // {"vi": "...", "en":"..."}

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "logos", columnDefinition = "jsonb", nullable = false)
    private Map<String, String> logos; // {"vi": "...", "en":"..."}

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "banners", columnDefinition = "jsonb")
    private Map<String, String> banners; // {"vi": "...", "en":"..."}

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "screenshots", columnDefinition = "jsonb")
    private Set<String> screenshots;

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "scopes", columnDefinition = "jsonb")
    private Set<MiniAppUserScope> scopes; // ["public_profile","phone","email","ekyc"]

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "permissions", columnDefinition = "jsonb")
    private Set<MiniAppPermission> permissions;
}
