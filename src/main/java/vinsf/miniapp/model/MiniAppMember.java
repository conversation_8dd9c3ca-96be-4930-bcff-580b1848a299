package vinsf.miniapp.model;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldNameConstants;
import vinsf.miniapp.constant.MiniAppMemberRole;
import vinsf.miniapp.constant.MiniAppMemberStatus;
import vinsf.miniapp.model.base.BaseEntityLong;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Entity
@Table(name = "miniapp_members")
@FieldNameConstants
public class MiniAppMember extends BaseEntityLong {
    public static final String ENTITY_NAME = "MINIAPP_MEMBER";

    @Column(name = "miniapp_id", nullable = false, updatable = false)
    private String miniappId;

    @Column(name = "superapp_user_id", nullable = false, updatable = false)
    private String superappUserId;

    @Column(name = "role", length = 16, nullable = false)
    @Enumerated(EnumType.STRING)
    private MiniAppMemberRole role;

    @Column(name = "status", length = 16)
    @Enumerated(EnumType.STRING)
    private MiniAppMemberStatus status;

}
