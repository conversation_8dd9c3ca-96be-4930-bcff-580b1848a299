package vinsf.miniapp.model;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import vinsf.miniapp.constant.BundleStatus;
import vinsf.miniapp.model.base.BaseEntityLong;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Entity
@Table(name = "miniapp_bundles")
public class MiniAppBundle extends BaseEntityLong {

    @Column(name = "miniapp_id", nullable = false)
    private String miniappId;

    @Column(name = "semver", length = 16, nullable = false, updatable = false)
    private String semver;

    @Column(name = "build_no",nullable = false, updatable = false)
    private Integer buildNo;

    @Column(name = "status", length = 16)
    @Enumerated(EnumType.STRING)
    private BundleStatus status;

    @Column(name = "storage_url", length = 1024, nullable = false)
    private String storageUrl;

    @Column(name = "checksum", length = 128, nullable = false)
    private String checksum;

    @Column(name = "size_bytes")
    private Long sizeBytes;

    @Column(name = "uploader", length = 100)
    private String uploader;

    @Column(name = "upload_at")
    private Long uploadAt;

    @Column(name = "miniapp_config_id")
    private Long miniappConfigId;

    @Column(name = "submitter", length = 100)
    private String submitter;

    @Column(name = "submitted_at")
    private Long submittedAt;

    @Column(name = "submit_notes", columnDefinition = "text")
    private String submitNotes;

    @Column(name = "reviewer", length = 100)
    private String reviewer;

    @Column(name = "reviewed_at")
    private Long reviewedAt;

    @Column(name = "review_notes", columnDefinition = "text")
    private String reviewNotes;
}
