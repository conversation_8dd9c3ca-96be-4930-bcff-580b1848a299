package vinsf.miniapp.model;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldNameConstants;
import vinsf.miniapp.constant.MiniAppMemberRole;
import vinsf.miniapp.constant.MiniAppMemberStatus;
import vinsf.miniapp.model.base.BaseEntityLong;
import vinsf.miniapp.model.base.BaseEntityUUID;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Entity
@Table(name = "miniapp_users")
@FieldNameConstants
public class MiniAppUser extends BaseEntityUUID {
    public static final String ENTITY_NAME = "MINIAPP_USER";

    @Column(name = "miniapp_id", nullable = false, updatable = false)
    private String miniappId;

    @Column(name = "superapp_user_id", nullable = false, updatable = false)
    private String superappUserId;

    @Column(name = "first_access_at")
    private Long firstAccessAt;

    @Column(name = "last_access_at")
    private Long lastAccessAt;

    @Column(name = "access_count")
    private Integer accessCount;

    @Column(name = "blocked")
    private Boolean blocked;
}
