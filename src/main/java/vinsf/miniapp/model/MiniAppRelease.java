package vinsf.miniapp.model;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldNameConstants;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;
import vinsf.miniapp.constant.ReleaseChannel;
import vinsf.miniapp.constant.ReleaseStatus;
import vinsf.miniapp.model.base.BaseEntityLong;

import java.util.Map;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Entity
@Table(name = "miniapp_releases")
@FieldNameConstants
public class MiniAppRelease extends BaseEntityLong {
    public static final String ENTITY_NAME = "MINIAPP_RELEASE";

    @Column(name = "miniapp_id", nullable = false)
    private String miniappId;

    @Column(name = "bundle_id", nullable = false)
    private Long bundleId;

    @Column(name = "channel", length = 8, nullable = false)
    private ReleaseChannel channel;

    @Column(name = "status", length = 16)
    @Enumerated(EnumType.STRING)
    private ReleaseStatus status;

    @Column(name = "releaser", length = 100)
    private String releaser;

    @Column(name = "released_at")
    private Long releasedAt;

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "release_notes", columnDefinition = "jsonb")
    private Map<String, String> releaseNotes; // {"vi": "...", "en":"..."}

}
