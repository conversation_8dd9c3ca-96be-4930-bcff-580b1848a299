package vinsf.miniapp.publisher;

import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import org.redisson.api.RTopic;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import vinsf.miniapp.dto.ChangeEvent;
import vinsf.miniapp.util.JsonUtil;

import java.util.Set;


@Component
@RequiredArgsConstructor
public class RedisPublisher {

    @Value("${redis.channel.change-event}")
    private String changeEventChannel;

    private final RedissonClient redissonClient;

    private final JsonUtil jsonUtil;

    private RTopic topic;

    @PostConstruct
    public void init() {
        topic = redissonClient.getTopic(changeEventChannel);
    }

    public void sendChangeEvent(String entityName, String id, String code) {
        var data = ChangeEvent.builder()
                .entity(entityName)
                .ids(Set.of(id))
                .codes(Set.of(code))
                .build();
        topic.publish(jsonUtil.toJson(data));
    }

    public void sendChangeEvent(String entityName, Set<String> ids, Set<String> codes) {
        var data = ChangeEvent.builder()
                .entity(entityName)
                .ids(ids)
                .codes(codes)
                .build();
        topic.publish(jsonUtil.toJson(data));
    }
}
