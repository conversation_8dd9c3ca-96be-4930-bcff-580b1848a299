package vinsf.miniapp.constant;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;

public enum BundleStatus {
    DRAFT, // after upload
    DEVELOP, // Deploy Develop: after CI build + scan + attestation
    TESTING, // Deploy Testing: after CI build + scan + attestation
    SUBMITTED, // after tester verify passed => Admin submit for review
    REVIEWING, // after admin submit for review
    APPROVED, // after reviewer approved
    REJECTED; //

    @JsonCreator
    public static BundleStatus fromName(String value) {
        for (BundleStatus status : BundleStatus.values()) {
            if (status.name().equalsIgnoreCase(value)) {
                return status;
            }
        }
        throw new IllegalArgumentException("Invalid BundleStatus value: " + value);
    }

    @JsonValue
    public String toName() {
        return name();
    }
}
