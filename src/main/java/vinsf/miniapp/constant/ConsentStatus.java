package vinsf.miniapp.constant;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;


public enum ConsentStatus {
    GRANTED,
    DENIED,
    REVOKED;

    @JsonCreator
    public static ConsentStatus fromName(String value) {
        for (ConsentStatus status : ConsentStatus.values()) {
            if (status.name().equalsIgnoreCase(value)) {
                return status;
            }
        }
        throw new IllegalArgumentException("Invalid ConsentStatus value: " + value);
    }

    @JsonValue
    public String toName() {
        return name();
    }
}
