package vinsf.miniapp.constant;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;


public enum ReleaseStatus {
    ACTIVE,
    PAUSED,
    ARCHIVED;

    @JsonCreator
    public static ReleaseStatus fromName(String value) {
        for (ReleaseStatus status : ReleaseStatus.values()) {
            if (status.name().equalsIgnoreCase(value)) {
                return status;
            }
        }
        throw new IllegalArgumentException("Invalid ReleaseStatus value: " + value);
    }

    @JsonValue
    public String toName() {
        return name();
    }
}
