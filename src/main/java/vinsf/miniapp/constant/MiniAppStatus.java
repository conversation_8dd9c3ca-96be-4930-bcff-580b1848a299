package vinsf.miniapp.constant;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;

public enum MiniAppStatus {
    REGISTERED,
    ACTIVE,
    INACTIVE;

    @JsonCreator
    public static MiniAppStatus fromName(String value) {
        for (MiniAppStatus status : MiniAppStatus.values()) {
            if (status.name().equalsIgnoreCase(value)) {
                return status;
            }
        }
        throw new IllegalArgumentException("Invalid MiniAppStatus value: " + value);
    }

    @JsonValue
    public String toName() {
        return name();
    }
}
