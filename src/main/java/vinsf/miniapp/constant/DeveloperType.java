package vinsf.miniapp.constant;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;

public enum DeveloperType {
    INDIVIDUAL,
    ORGANIZATION
    ;

    @JsonCreator
    public static DeveloperType fromName(String value) {
        for (DeveloperType type : DeveloperType.values()) {
            if (type.name().equalsIgnoreCase(value)) {
                return type;
            }
        }
        throw new IllegalArgumentException("Invalid DeveloperType value: " + value);
    }

    @JsonValue
    public String toName() {
        return name();
    }
}
