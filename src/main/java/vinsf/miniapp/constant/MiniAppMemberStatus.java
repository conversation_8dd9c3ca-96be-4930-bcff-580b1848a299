package vinsf.miniapp.constant;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;

public enum MiniAppMemberStatus {
    ACTIVE,
    INACTIVE
    ;

    @JsonCreator
    public static MiniAppMemberStatus fromName(String value) {
        for (MiniAppMemberStatus type : MiniAppMemberStatus.values()) {
            if (type.name().equalsIgnoreCase(value)) {
                return type;
            }
        }
        throw new IllegalArgumentException("Invalid MiniAppMemberStatus value: " + value);
    }

    @JsonValue
    public String toName() {
        return name();
    }
}
