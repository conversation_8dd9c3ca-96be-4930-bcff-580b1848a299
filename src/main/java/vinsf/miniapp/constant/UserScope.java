package vinsf.miniapp.constant;

import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum UserScope {
    PUBLIC_PROFILE("public_profile"),
    PHONE("phone"),
    EMAIL("email"),
    KYC("ekyc"),
    LOCATION("location"),
    ;

    private final String code;

    @JsonValue
    public String toCode() {
        return code;
    }

    public static UserScope fromCode(String name) {
        for (UserScope scope : UserScope.values()) {
            if (scope.code.equalsIgnoreCase(name)) {
                return scope;
            }
        }
        throw new IllegalArgumentException("Invalid UserScope value: " + name);
    }


}
