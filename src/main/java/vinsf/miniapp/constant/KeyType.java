package vinsf.miniapp.constant;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum KeyType {
    SIGNING("signing"),
    ENC<PERSON>YPTION("encryption"),
    ;

    private final String code;

    @JsonCreator
    public static KeyType fromCode(String value) {
        for (KeyType type : KeyType.values()) {
            if (type.code.equalsIgnoreCase(value)) {
                return type;
            }
        }
        throw new IllegalArgumentException("Invalid KeyType value: " + value);
    }

    @JsonValue
    public String toCode() {
        return code;
    }
}
