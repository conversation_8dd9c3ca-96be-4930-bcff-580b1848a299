package vinsf.miniapp.constant;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;


public enum DeveloperStatus {
    ACTIVE,
    INACTIVE,
    PENDING,
    REJECTED;

    @JsonCreator
    public static DeveloperStatus fromName(String value) {
        for (DeveloperStatus status : DeveloperStatus.values()) {
            if (status.name().equalsIgnoreCase(value)) {
                return status;
            }
        }
        throw new IllegalArgumentException("Invalid DeveloperStatus value: " + value);
    }

    @JsonValue
    public String toName() {
        return name();
    }
}
