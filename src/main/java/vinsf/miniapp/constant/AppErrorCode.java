package vinsf.miniapp.constant;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@RequiredArgsConstructor
public enum AppErrorCode {

    //--
    SUCCESS(0, "Success"),
    ERROR(-1, "Error"),

    BAD_REQUEST(400, "Bad Request"),
    UNAUTHORIZED(401, "Unauthorized"),
    FORBIDDEN(403, "Forbidden"),
    NOT_FOUND(404, "Resource not found"),
    METHOD_NOT_ALLOWED(405, "Method Not Allowed"),
    MEDIA_TYPE_NOT_ACCEPTABLE(406, "Media Type Not Acceptable"),
    UNSUPPORTED_MEDIA_TYPE(415, "Unsupported Media Type"),
    TOO_MANY_REQUESTS(429, "Too Many Requests"),
    INTERNAL_SERVER_ERROR(500, "Internal Server Error"),

    // Common errors
    RESOURCE_NOT_FOUND(1000001, "%s not found with %s=%s"),
    RESOURCE_ALREADY_EXISTS(1000002, "%s already exists with %s=%s"),
    INVALID_STATUS(1000003, "Invalid status for %s. Expected one of [%s], but got [%s]"),

    // MiniApp Management Service (1001xxx)
    MINIAPP_KMS_ENCRYPTION_FAILED(1001001, "Failed to encrypt data using KMS"),
    MINIAPP_KMS_DECRYPTION_FAILED(1001002, "Failed to decrypt data using KMS"),
    MINIAPP_KEYPAIR_GENERATION_FAILED(1001003, "Failed to generate keypair"),
    MINIAPP_SECRET_GENERATION_FAILED(1001004, "Failed to generate secret"),
    MINIAPP_CANNOT_REGENERATE_SECRET_ACTIVE(1001005, "Cannot regenerate secret for active MiniApp"),


    // MiniApp Bundle Service (1002)
    // MiniApp User Service (1003)
    ;

    private final int code;
    private final String message;

}
