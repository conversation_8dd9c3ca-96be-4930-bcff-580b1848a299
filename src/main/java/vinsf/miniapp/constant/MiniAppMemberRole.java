package vinsf.miniapp.constant;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;

public enum MiniAppMemberRole {
    OWNER,
    ADMIN,
    DEVELOPER,
    TESTER
    ;

    @JsonCreator
    public static MiniAppMemberRole fromName(String value) {
        for (MiniAppMemberRole type : MiniAppMemberRole.values()) {
            if (type.name().equalsIgnoreCase(value)) {
                return type;
            }
        }
        throw new IllegalArgumentException("Invalid MiniAppMemberRole value: " + value);
    }

    @JsonValue
    public String toName() {
        return name();
    }
}
