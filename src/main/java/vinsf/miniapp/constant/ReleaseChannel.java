package vinsf.miniapp.constant;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ReleaseChannel {
    TEST("test"),
    CANARY("canary"),
    STABLE("stable")
    ;

    private final String code;

    @JsonCreator
    public static ReleaseChannel fromCode(String value) {
        for (ReleaseChannel type : ReleaseChannel.values()) {
            if (type.code.equalsIgnoreCase(value)) {
                return type;
            }
        }
        throw new IllegalArgumentException("Invalid Environment value: " + value);
    }

    @JsonValue
    public String toCode() {
        return code;
    }
}
