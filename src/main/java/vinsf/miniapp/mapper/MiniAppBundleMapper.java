package vinsf.miniapp.mapper;

import org.mapstruct.*;
import org.mapstruct.factory.Mappers;
import vinsf.miniapp.dto.MiniAppBundleDto;
import vinsf.miniapp.model.MiniAppBundle;

/**
 * MapStruct mapper for MiniAppBundle entity and DTO
 *
 * <AUTHOR>
 * @since 2025-10-02
 */
@Mapper(componentModel = "spring")
public interface MiniAppBundleMapper {
    MiniAppBundleMapper INSTANCE = Mappers.getMapper(MiniAppBundleMapper.class);

    /**
     * Convert entity to DTO
     *
     * @param entity the MiniAppBundle entity
     * @return MiniAppBundleDto
     */
    MiniAppBundleDto toDto(MiniAppBundle entity);

    /**
     * Convert DTO to entity
     *
     * @param dto the MiniAppBundleDto
     * @return MiniAppBundle entity
     */
    MiniAppBundle toEntity(MiniAppBundleDto dto);

    /**
     * Update entity from DTO (for partial updates)
     * Only non-null fields in DTO will be copied to entity
     *
     * @param dto    the source DTO
     * @param entity the target entity to update
     */
    @Mappings({
            @Mapping(target = "id", ignore = true),
            @Mapping(target = "miniappId", ignore = true),
            @Mapping(target = "semver", ignore = true),
            @Mapping(target = "buildNo", ignore = true),
            @Mapping(target = "createdOn", ignore = true),
            @Mapping(target = "createdBy", ignore = true),
            @Mapping(target = "version", ignore = true),
            @Mapping(target = "updatedOn", ignore = true)
    })
    @BeanMapping(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
    void partialUpdate(@MappingTarget MiniAppBundle entity, MiniAppBundleDto dto);

    /**
     * Update entity from DTO (for full updates)
     *
     * @param dto    the source DTO
     * @param entity the target entity to update
     */
    @Mappings({
            @Mapping(target = "id", ignore = true),
            @Mapping(target = "miniappId", ignore = true),
            @Mapping(target = "semver", ignore = true),
            @Mapping(target = "buildNo", ignore = true),
            @Mapping(target = "createdOn", ignore = true),
            @Mapping(target = "createdBy", ignore = true),
            @Mapping(target = "version", ignore = true),
            @Mapping(target = "updatedOn", ignore = true)
    })
    void update(@MappingTarget MiniAppBundle entity, MiniAppBundleDto dto);
}
