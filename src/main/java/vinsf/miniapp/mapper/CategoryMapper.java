package vinsf.miniapp.mapper;

import org.mapstruct.*;
import org.mapstruct.factory.Mappers;
import vinsf.miniapp.dto.CategoryDto;
import vinsf.miniapp.model.Category;

/**
 * MapStruct mapper for Category entity and DTO
 *
 * <AUTHOR>
 * @since 2025-10-02
 */
@Mapper(componentModel = "spring")
public interface CategoryMapper {
    CategoryMapper INSTANCE = Mappers.getMapper(CategoryMapper.class);

    /**
     * Convert entity to DTO
     *
     * @param entity the Category entity
     * @return CategoryDto
     */
    CategoryDto toDto(Category entity);

    /**
     * Convert DTO to entity
     *
     * @param dto the CategoryDto
     * @return Category entity
     */
    Category toEntity(CategoryDto dto);

    /**
     * Update entity from DTO (for partial updates)
     * Only non-null fields in DTO will be copied to entity
     *
     * @param dto    the source DTO
     * @param entity the target entity to update
     */
    @Mappings({
            @Mapping(target = "id", ignore = true),
            @Mapping(target = "code", ignore = true),
            @Mapping(target = "createdOn", ignore = true),
            @Mapping(target = "createdBy", ignore = true),
            @Mapping(target = "version", ignore = true),
            @Mapping(target = "updatedOn", ignore = true)
    })
    @BeanMapping(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
    void partialUpdate(@MappingTarget Category entity, CategoryDto dto);

    /**
     * Update entity from DTO (for full updates)
     *
     * @param dto    the source DTO
     * @param entity the target entity to update
     */
    @Mappings({
            @Mapping(target = "id", ignore = true),
            @Mapping(target = "code", ignore = true),
            @Mapping(target = "createdOn", ignore = true),
            @Mapping(target = "createdBy", ignore = true),
            @Mapping(target = "version", ignore = true),
            @Mapping(target = "updatedOn", ignore = true)
    })
    void update(@MappingTarget Category entity, CategoryDto dto);
}
