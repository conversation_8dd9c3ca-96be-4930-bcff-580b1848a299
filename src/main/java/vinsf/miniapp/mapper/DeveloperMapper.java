package vinsf.miniapp.mapper;

import org.mapstruct.*;
import org.mapstruct.factory.Mappers;
import vinsf.miniapp.dto.DeveloperDto;
import vinsf.miniapp.model.Developer;

/**
 * MapStruct mapper for Developer entity and DTO
 *
 * <AUTHOR>
 * @since 2025-10-02
 */
@Mapper(componentModel = "spring")
public interface DeveloperMapper {
    DeveloperMapper INSTANCE = Mappers.getMapper(DeveloperMapper.class);

    /**
     * Convert entity to DTO
     *
     * @param entity the Developer entity
     * @return DeveloperDto
     */
    DeveloperDto toDto(Developer entity);

    /**
     * Convert DTO to entity
     *
     * @param dto the DeveloperDto
     * @return Developer entity
     */
    Developer toEntity(DeveloperDto dto);

    /**
     * Update entity from DTO (for partial updates)
     * Only non-null fields in DTO will be copied to entity
     *
     * @param dto    the source DTO
     * @param entity the target entity to update
     */
    @Mappings({
            @Mapping(target = "id", ignore = true),
            @Mapping(target = "superappUserId", ignore = true),
            @Mapping(target = "createdOn", ignore = true),
            @Mapping(target = "createdBy", ignore = true),
            @Mapping(target = "version", ignore = true),
            @Mapping(target = "updatedOn", ignore = true)
    })
    @BeanMapping(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
    void partialUpdate(@MappingTarget Developer entity, DeveloperDto dto);

    /**
     * Update entity from DTO (for full updates)
     *
     * @param dto    the source DTO
     * @param entity the target entity to update
     */
    @Mappings({
            @Mapping(target = "id", ignore = true),
            @Mapping(target = "superappUserId", ignore = true),
            @Mapping(target = "createdOn", ignore = true),
            @Mapping(target = "createdBy", ignore = true),
            @Mapping(target = "version", ignore = true),
            @Mapping(target = "updatedOn", ignore = true)
    })
    void update(@MappingTarget Developer entity, DeveloperDto dto);
}
